# Windows 部署指南

## 📋 系统要求

### 最低要求
- Windows 10 或更高版本
- 4GB RAM
- 2GB 可用磁盘空间
- 网络连接

### 推荐配置
- Windows 11
- 8GB RAM 或更多
- SSD 硬盘
- 稳定的网络连接

## 🚀 部署方式

### 方式一：Docker 部署（推荐）

#### 1. 安装 Docker Desktop
1. 访问 [Docker Desktop 官网](https://www.docker.com/products/docker-desktop/)
2. 下载 Windows 版本
3. 运行安装程序，按提示完成安装
4. 重启计算机
5. 启动 Docker Desktop，等待初始化完成

#### 2. 下载项目
```bash
# 使用 Git 克隆（推荐）
git clone <repository-url>
cd iptv-player

# 或下载 ZIP 文件并解压
```

#### 3. 启动应用
```bash
# 方法一：使用启动脚本（推荐）
.\scripts\start.bat

# 方法二：手动启动
docker-compose up -d --build
```

#### 4. 访问应用
打开浏览器访问：`http://localhost:9004`

### 方式二：本地 Node.js 部署

#### 1. 安装 Node.js
1. 访问 [Node.js 官网](https://nodejs.org/)
2. 下载 LTS 版本（推荐 18.x 或更高）
3. 运行安装程序，确保勾选"Add to PATH"
4. 打开命令提示符，验证安装：
```bash
node --version
npm --version
```

#### 2. 安装依赖
```bash
cd iptv-player
npm install
```

#### 3. 配置数据库
```bash
npx prisma generate
npx prisma db push
```

#### 4. 配置端口（可选）
创建 `.env.local` 文件：
```
PORT=9004
NEXTAUTH_URL=http://localhost:9004
```

#### 5. 启动应用
```bash
# 开发模式
npm run dev

# 生产模式
npm run build
npm run start
```

## 🔧 配置说明

### 端口配置

#### 修改 Docker 端口
编辑 `docker-compose.yml`：
```yaml
services:
  app:
    ports:
      - "8080:3000"  # 修改外部端口为 8080
    environment:
      - NEXTAUTH_URL=http://localhost:8080  # 同步修改
```

#### 修改本地开发端口
方法一：环境变量
```bash
set PORT=8080 && npm run dev
```

方法二：修改 `.env.local`
```
PORT=8080
NEXTAUTH_URL=http://localhost:8080
```

### 数据存储

#### Docker 部署
- 数据库文件：`./data/dev.db`
- 配置文件：`./config/`（可选）

#### 本地部署
- 数据库文件：项目根目录下的 `dev.db`

## 🔧 故障排除

### 常见问题

#### 1. Docker Desktop 启动失败
**症状**：Docker Desktop 无法启动或卡在启动界面
**解决方案**：
1. 确保已启用 Windows 虚拟化功能
2. 在"Windows 功能"中启用"Hyper-V"和"容器"
3. 重启计算机
4. 以管理员身份运行 Docker Desktop

#### 2. 端口被占用
**症状**：启动时提示端口 9004 被占用
**解决方案**：
```bash
# 查看端口占用
netstat -ano | findstr :9004

# 结束占用进程（替换 PID）
taskkill /PID <PID> /F

# 或修改为其他端口
```

#### 3. Node.js 安装问题
**症状**：命令行无法识别 node 或 npm 命令
**解决方案**：
1. 重新安装 Node.js，确保勾选"Add to PATH"
2. 重启命令提示符
3. 手动添加到系统 PATH 环境变量

#### 4. 权限问题
**症状**：npm install 失败，提示权限错误
**解决方案**：
```bash
# 以管理员身份运行命令提示符
# 或使用 PowerShell（推荐）
```

#### 5. 防火墙阻止
**症状**：应用启动成功但无法访问
**解决方案**：
1. 检查 Windows 防火墙设置
2. 允许 Node.js 或 Docker 通过防火墙
3. 临时关闭防火墙测试

### 性能优化

#### Docker 性能优化
1. 分配足够的内存给 Docker Desktop（推荐 4GB+）
2. 启用 WSL 2 后端（Windows 10/11）
3. 将项目文件放在 WSL 文件系统中（如果使用 WSL）

#### 本地开发优化
1. 使用 SSD 硬盘
2. 关闭不必要的后台程序
3. 定期清理 npm 缓存：`npm cache clean --force`

## 📝 维护指南

### 更新应用
```bash
# Docker 部署
docker-compose down
git pull
docker-compose up -d --build

# 本地部署
git pull
npm install
npm run build
```

### 备份数据
```bash
# 备份数据库文件
copy data\dev.db backup\dev.db.backup

# 或使用 PowerShell
Copy-Item data\dev.db backup\dev.db.backup
```

### 日志查看
```bash
# Docker 日志
docker-compose logs app

# 本地开发日志
# 查看终端输出和浏览器控制台
```

## 🔗 相关链接

- [Docker Desktop 下载](https://www.docker.com/products/docker-desktop/)
- [Node.js 下载](https://nodejs.org/)
- [Git for Windows](https://git-scm.com/download/win)
- [Visual Studio Code](https://code.visualstudio.com/)（推荐编辑器）

## 📞 技术支持

如果遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查项目的 GitHub Issues
3. 查看应用日志获取详细错误信息

import { NextRequest, NextResponse } from 'next/server'
import { DatabaseOperations } from '@/lib/database'
import { ApiResponse, Channel, SearchFilters } from '@/types'

// GET /api/channels - 获取频道列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // 解析查询参数
    const filters: SearchFilters = {
      query: searchParams.get('query') || undefined,
      group: searchParams.get('group') || undefined,
      source: searchParams.get('source') || undefined,
      sortBy: (searchParams.get('sortBy') as 'name' | 'group' | 'createdAt') || 'name',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'asc',
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '10000')
    }

    // 获取频道数据
    const sourceId = filters.source
    const groupName = filters.group
    const dbChannels = await DatabaseOperations.getChannels(sourceId, groupName)
    
    // 转换数据库格式到API格式
    let channels: Channel[] = dbChannels.map(channel => ({
      id: channel.id,
      name: channel.name,
      url: channel.url,
      group: channel.group_name,
      logo: channel.logo_url || undefined,
      sourceId: channel.source_id,
      createdAt: new Date(channel.created_at),
      updatedAt: new Date(channel.updated_at)
    }))

    // 应用搜索过滤
    if (filters.query) {
      const query = filters.query.toLowerCase()
      channels = channels.filter(channel =>
        channel.name.toLowerCase().includes(query) ||
        channel.group.toLowerCase().includes(query)
      )
    }

    // 应用排序
    channels.sort((a, b) => {
      let comparison = 0
      
      switch (filters.sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'group':
          comparison = a.group.localeCompare(b.group) || a.name.localeCompare(b.name)
          break
        case 'createdAt':
          comparison = a.createdAt.getTime() - b.createdAt.getTime()
          break
        default:
          comparison = 0
      }
      
      return filters.sortOrder === 'desc' ? -comparison : comparison
    })

    // 应用分页
    const startIndex = (filters.page! - 1) * filters.limit!
    const endIndex = startIndex + filters.limit!
    const paginatedChannels = channels.slice(startIndex, endIndex)

    // 构建响应
    const response: ApiResponse<{
      channels: Channel[]
      pagination: {
        page: number
        limit: number
        total: number
        totalPages: number
      }
      filters: SearchFilters
    }> = {
      success: true,
      data: {
        channels: paginatedChannels,
        pagination: {
          page: filters.page!,
          limit: filters.limit!,
          total: channels.length,
          totalPages: Math.ceil(channels.length / filters.limit!)
        },
        filters
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Failed to get channels:', error)
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get channels',
      message: error instanceof Error ? error.message : 'Unknown error'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// POST /api/channels - 添加单个频道
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, url, group, logo, sourceId } = body

    // 验证必需字段
    if (!name || !url) {
      const response: ApiResponse = {
        success: false,
        error: 'Missing required fields',
        message: 'Name and URL are required'
      }
      return NextResponse.json(response, { status: 400 })
    }

    // 验证URL格式
    try {
      new URL(url)
    } catch {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid URL',
        message: 'Please provide a valid URL'
      }
      return NextResponse.json(response, { status: 400 })
    }

    // 如果指定了sourceId，验证源是否存在
    if (sourceId) {
      const source = await DatabaseOperations.getSource(sourceId)
      if (!source) {
        const response: ApiResponse = {
          success: false,
          error: 'Source not found',
          message: `Source with id ${sourceId} not found`
        }
        return NextResponse.json(response, { status: 404 })
      }
    }

    // 创建频道
    const channelId = await DatabaseOperations.createChannel({
      name: name.trim(),
      url: url.trim(),
      group_name: (group || 'Default').trim(),
      logo_url: logo?.trim() || null,
      source_id: sourceId || 'manual'
    })

    // 获取创建的频道
    const createdChannel = await DatabaseOperations.getChannel(channelId)
    if (!createdChannel) {
      throw new Error('Failed to retrieve created channel')
    }

    const apiChannel: Channel = {
      id: createdChannel.id,
      name: createdChannel.name,
      url: createdChannel.url,
      group: createdChannel.group_name,
      logo: createdChannel.logo_url || undefined,
      sourceId: createdChannel.source_id,
      createdAt: new Date(createdChannel.created_at),
      updatedAt: new Date(createdChannel.updated_at)
    }

    const response: ApiResponse<Channel> = {
      success: true,
      data: apiChannel,
      message: 'Channel created successfully'
    }

    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error('Failed to create channel:', error)
    const response: ApiResponse = {
      success: false,
      error: 'Failed to create channel',
      message: error instanceof Error ? error.message : 'Unknown error'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

const testStreams = [
  {
    name: 'Apple HLS Test Stream',
    url: 'https://devstreaming-cdn.apple.com/videos/streaming/examples/img_bipbop_adv_example_fmp4/master.m3u8',
    group: '测试流源',
    logo: 'https://developer.apple.com/assets/elements/icons/hls/hls-96x96_2x.png'
  },
  {
    name: 'Mux Test Stream',
    url: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
    group: '测试流源',
    logo: 'https://mux.com/favicon.ico'
  },
  {
    name: 'Big <PERSON> Bunny',
    url: 'https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4',
    group: '测试流源',
    logo: 'https://peach.blender.org/wp-content/uploads/bbb-splash.png'
  },
  {
    name: 'Sintel Trailer',
    url: 'https://bitdash-a.akamaihd.net/content/sintel/hls/playlist.m3u8',
    group: '测试流源',
    logo: 'https://durian.blender.org/wp-content/uploads/2010/06/sintel_trailer_1080p.png'
  },
  {
    name: 'Tears of Steel',
    url: 'https://bitdash-a.akamaihd.net/content/MI201109210084_1/m3u8s/f08e80da-bf1d-4e3d-8899-f0f6155f6efa.m3u8',
    group: '测试流源',
    logo: 'https://mango.blender.org/wp-content/uploads/2012/09/tos-poster.jpg'
  },
  {
    name: 'Elephant Dream',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    group: '测试流源',
    logo: 'https://orange.blender.org/wp-content/uploads/2006/05/elephants_dream_poster.jpg'
  },
  {
    name: 'CCTV1 Test',
    url: 'http://*************/dbiptv.sn.chinamobile.com/PLTV/88888890/224/3221225804/index.m3u8',
    group: '测试流源',
    logo: 'https://upload.wikimedia.org/wikipedia/zh/6/65/CCTV-1_Logo.png'
  },
  {
    name: 'NASA Live',
    url: 'https://nasa-i.akamaihd.net/hls/live/253565/NASA-NTV1-HLS/master.m3u8',
    group: '测试流源',
    logo: 'https://www.nasa.gov/sites/default/files/thumbnails/image/nasa-logo-web-rgb.png'
  },
  {
    name: 'Weather Channel',
    url: 'https://weather-lh.akamaihd.net/i/twc_1@92006/master.m3u8',
    group: '测试流源',
    logo: 'https://s.w-x.co/staticmaps/wu/wu/wxtype1200_cur/usen/animate.png'
  },
  {
    name: 'Red Bull TV',
    url: 'https://rbmn-live.akamaized.net/hls/live/590964/BoRB-AT/master.m3u8',
    group: '测试流源',
    logo: 'https://img.redbull.com/images/c_crop,w_1200,h_630,x_0,y_0,f_auto,q_auto/c_scale,w_1200/redbullcom/2019/05/29/d4c0c7e5-7ee9-4b8e-9f5c-9e0b4c0c7e5d/red-bull-tv-logo'
  }
]

async function addTestStreams() {
  try {
    console.log('开始添加测试流源...')
    
    // 首先创建一个测试订阅源
    const testSubscription = await prisma.subscription.upsert({
      where: { url: 'internal://test-streams' },
      update: {
        name: '测试流源',
        type: 'internal',
        isActive: true,
        lastUpdated: new Date()
      },
      create: {
        name: '测试流源',
        url: 'internal://test-streams',
        type: 'internal',
        isActive: true,
        lastUpdated: new Date()
      }
    })
    
    console.log('测试订阅源已创建:', testSubscription.id)
    
    // 添加测试频道
    for (const stream of testStreams) {
      // 先检查是否已存在
      const existingChannel = await prisma.channel.findFirst({
        where: {
          name: stream.name,
          subscriptionId: testSubscription.id
        }
      })

      if (existingChannel) {
        // 更新现有频道
        const channel = await prisma.channel.update({
          where: { id: existingChannel.id },
          data: {
            url: stream.url,
            group: stream.group,
            logo: stream.logo
          }
        })
        console.log('已更新测试频道:', channel.name)
      } else {
        // 创建新频道
        const channel = await prisma.channel.create({
          data: {
            name: stream.name,
            url: stream.url,
            group: stream.group,
            logo: stream.logo,
            subscriptionId: testSubscription.id,
            isFavorite: false
          }
        })
        console.log('已添加测试频道:', channel.name)
      }
    }
    
    console.log('所有测试流源添加完成!')
    
    // 显示统计信息
    const totalChannels = await prisma.channel.count()
    const testChannels = await prisma.channel.count({
      where: { group: '测试流源' }
    })
    
    console.log(`总频道数: ${totalChannels}`)
    console.log(`测试频道数: ${testChannels}`)
    
  } catch (error) {
    console.error('添加测试流源时出错:', error)
  } finally {
    await prisma.$disconnect()
  }
}

addTestStreams()

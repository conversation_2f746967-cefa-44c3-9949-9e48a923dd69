'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Settings, Menu, X, Plus, Tv, Wifi, WifiOff, Refresh<PERSON>w, Clock } from 'lucide-react'
import Player from '@/components/Player'
import ChannelList from '@/components/ChannelList'
import SourceManager from '@/components/SourceManager'
import SettingsPanel from '@/components/SettingsPanel'
import ThemeToggle from '@/components/ThemeToggle'
import ErrorBoundary from '@/components/ErrorBoundary'
import { Channel, Source, ApiResponse, AppError } from '@/types'
import { cn, handleError } from '@/lib/utils'

export default function HomePage() {
  // 状态管理
  const [channels, setChannels] = useState<Channel[]>([])
  const [sources, setSources] = useState<Source[]>([])
  const [selectedChannel, setSelectedChannel] = useState<Channel | undefined>()
  const [activeTab, setActiveTab] = useState<'channels' | 'sources'>('channels')
  const [showSettings, setShowSettings] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isOnline, setIsOnline] = useState(true)

  // 定时刷新相关状态
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false)
  const [refreshInterval, setRefreshInterval] = useState(5) // 分钟
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)

  // 单链接播放相关状态
  const [directUrl, setDirectUrl] = useState('')
  const [isDirectMode, setIsDirectMode] = useState(false)

  // 网络状态监听
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)
    
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    
    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // 初始化数据
  useEffect(() => {
    loadInitialData()
  }, [])

  const loadInitialData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      await Promise.all([
        loadSources(),
        loadChannels()
      ])
    } catch (error) {
      console.error('Failed to load initial data:', error)
      setError('加载数据失败，请检查网络连接')
    } finally {
      setLoading(false)
    }
  }

  const loadSources = async () => {
    try {
      const response = await fetch('/api/sources')
      const data: ApiResponse<Source[]> = await response.json()
      
      if (data.success && data.data) {
        setSources(data.data)
      } else {
        throw new Error(data.error || '获取订阅源失败')
      }
    } catch (error) {
      console.error('Failed to load sources:', error)
      throw error
    }
  }

  const loadChannels = async () => {
    try {
      const response = await fetch('/api/channels')
      const data: ApiResponse<{channels: Channel[]}> = await response.json()

      if (data.success && data.data) {
        setChannels(data.data.channels)
        // 注意：不在这里更新lastRefreshTime，只有真正刷新订阅源时才更新
      } else {
        throw new Error(data.error || '获取频道列表失败')
      }
    } catch (error) {
      console.error('Failed to load channels:', error)
      throw error
    }
  }

  // 刷新所有订阅源（真正的刷新）
  const refreshAllSources = async () => {
    const activeSources = sources.filter(source => source.status === 'active')
    const refreshPromises = activeSources.map(async (source) => {
      try {
        const response = await fetch(`/api/sources/${source.id}/refresh`, {
          method: 'POST',
        })
        const data: ApiResponse<Source> = await response.json()
        if (data.success && data.data) {
          return data.data
        }
        throw new Error(data.error || '刷新失败')
      } catch (error) {
        console.error(`Failed to refresh source ${source.name}:`, error)
        return source // 返回原始数据，避免中断其他刷新
      }
    })

    const refreshedSources = await Promise.all(refreshPromises)

    // 更新订阅源状态
    setSources(prev => prev.map(source => {
      const refreshed = refreshedSources.find(rs => rs.id === source.id)
      return refreshed || source
    }))

    // 重新加载频道列表
    await loadChannels()
  }

  // 手动刷新（真正刷新所有订阅源）
  const handleManualRefresh = useCallback(async () => {
    if (isRefreshing) return

    setIsRefreshing(true)
    try {
      // 先加载订阅源列表，然后刷新所有订阅源
      await loadSources()
      await refreshAllSources()
      setLastRefreshTime(new Date()) // 只有在真正刷新后才更新时间
    } catch (error) {
      console.error('Manual refresh failed:', error)
      setError('刷新失败，请检查网络连接')
    } finally {
      setIsRefreshing(false)
    }
  }, [isRefreshing, sources])

  // 定时刷新逻辑（真正刷新所有订阅源）
  useEffect(() => {
    if (!autoRefreshEnabled) return

    const intervalMs = refreshInterval * 60 * 1000 // 转换为毫秒
    const timer = setInterval(async () => {
      if (!isRefreshing && isOnline) {
        setIsRefreshing(true)
        try {
          await loadSources()
          await refreshAllSources()
          setLastRefreshTime(new Date()) // 只有在真正刷新后才更新时间
        } catch (error) {
          console.error('Auto refresh failed:', error)
        } finally {
          setIsRefreshing(false)
        }
      }
    }, intervalMs)

    return () => clearInterval(timer)
  }, [autoRefreshEnabled, refreshInterval, isRefreshing, isOnline, sources])

  // 单链接播放相关函数
  const validateUrl = (url: string): boolean => {
    try {
      const urlObj = new URL(url)
      return ['http:', 'https:'].includes(urlObj.protocol)
    } catch {
      return false
    }
  }

  const handleDirectUrlPlay = () => {
    if (!directUrl.trim()) return

    if (!validateUrl(directUrl)) {
      setError('请输入有效的URL地址')
      return
    }

    // 创建临时频道对象
    const tempChannel: Channel = {
      id: 'direct-url',
      name: '直播链接',
      url: directUrl.trim(),
      logo: '',
      group: '直播链接',
      sourceId: 'direct'
    }

    setSelectedChannel(tempChannel)
    setIsDirectMode(true)
    setError(null)

    // 在移动端播放后关闭侧边栏
    if (window.innerWidth < 768) {
      setSidebarOpen(false)
    }
  }

  const handleClearDirectUrl = () => {
    setDirectUrl('')
    setIsDirectMode(false)
    setSelectedChannel(undefined)
  }

  const handleChannelSelect = (channel: Channel) => {
    setSelectedChannel(channel)
    setIsDirectMode(false) // 退出直播模式
    // 在移动端选择频道后关闭侧边栏
    if (window.innerWidth < 768) {
      setSidebarOpen(false)
    }
  }

  const handleSourceAdd = async (sourceData: Omit<Source, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const response = await fetch('/api/sources', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sourceData),
      })
      
      const data: ApiResponse<Source> = await response.json()
      
      if (data.success && data.data) {
        setSources(prev => [...prev, data.data!])
        await loadChannels() // 重新加载频道列表
      } else {
        throw new Error(data.error || '添加订阅源失败')
      }
    } catch (error) {
      console.error('Failed to add source:', error)
      throw new Error(handleError(error))
    }
  }

  const handleSourceUpdate = async (id: string, updates: Partial<Source>) => {
    try {
      const response = await fetch(`/api/sources/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })
      
      const data: ApiResponse<Source> = await response.json()
      
      if (data.success && data.data) {
        setSources(prev => prev.map(source => 
          source.id === id ? data.data! : source
        ))
        await loadChannels() // 重新加载频道列表
      } else {
        throw new Error(data.error || '更新订阅源失败')
      }
    } catch (error) {
      console.error('Failed to update source:', error)
      throw new Error(handleError(error))
    }
  }

  const handleSourceDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/sources/${id}`, {
        method: 'DELETE',
      })
      
      const data: ApiResponse = await response.json()
      
      if (data.success) {
        setSources(prev => prev.filter(source => source.id !== id))
        await loadChannels() // 重新加载频道列表
        // 如果当前选中的频道属于被删除的源，清除选择
        if (selectedChannel && selectedChannel.sourceId === id) {
          setSelectedChannel(undefined)
        }
      } else {
        throw new Error(data.error || '删除订阅源失败')
      }
    } catch (error) {
      console.error('Failed to delete source:', error)
      throw new Error(handleError(error))
    }
  }

  const handleSourceRefresh = async (id: string) => {
    try {
      const response = await fetch(`/api/sources/${id}/refresh`, {
        method: 'POST',
      })

      const data: ApiResponse<Source> = await response.json()

      if (data.success && data.data) {
        setSources(prev => prev.map(source =>
          source.id === id ? data.data! : source
        ))
        await loadChannels() // 重新加载频道列表

        // 如果这是单个订阅源刷新，也更新全局刷新时间
        // 这样可以保持时间显示的一致性
        setLastRefreshTime(new Date())
      } else {
        throw new Error(data.error || '刷新订阅源失败')
      }
    } catch (error) {
      console.error('Failed to refresh source:', error)
      throw new Error(handleError(error))
    }
  }

  const handlePlayerError = (error: AppError) => {
    console.error('Player error:', error)
    setError(`播放错误: ${error.message}`)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center" id="main-loading-screen">
        <div className="text-center">
          <div
            className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"
            id="main-loading-spinner"
          />
          <div className="text-lg font-medium text-foreground">加载中...</div>
          <div className="text-sm text-muted-foreground">正在初始化IPTV播放器</div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* 顶部导航栏 */}
      <header className="bg-card border-b border-border px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 hover:bg-accent rounded-lg transition-colors lg:hidden"
          >
            {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </button>
          
          <div className="flex items-center space-x-2">
            <Tv className="w-6 h-6 text-primary" />
            <h1 className="text-xl font-bold">IPTV Player</h1>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* 刷新按钮 */}
          <button
            onClick={handleManualRefresh}
            disabled={isRefreshing}
            className="p-2 hover:bg-accent rounded-lg transition-colors disabled:opacity-50"
            title="刷新所有订阅源并更新频道列表"
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </button>

          {/* 最后刷新时间 */}
          {lastRefreshTime && (
            <div className="hidden sm:flex items-center space-x-1 text-xs text-muted-foreground">
              <Clock className="w-3 h-3" />
              <span title="上次刷新所有订阅源的时间">
                {lastRefreshTime.toLocaleTimeString()}
              </span>
            </div>
          )}

          {/* 网络状态指示器 */}
          <div className="flex items-center space-x-1">
            {isOnline ? (
              <Wifi className="w-4 h-4 text-green-500" />
            ) : (
              <WifiOff className="w-4 h-4 text-red-500" />
            )}
            <span className="text-xs text-muted-foreground hidden sm:inline">
              {isOnline ? '在线' : '离线'}
            </span>
          </div>

          <ThemeToggle />

          <button
            onClick={() => setShowSettings(true)}
            className="p-2 hover:bg-accent rounded-lg transition-colors"
          >
            <Settings className="w-5 h-5" />
          </button>
        </div>
      </header>

      <div className="flex h-[calc(100vh-73px)]">
        {/* 侧边栏 */}
        <aside className={cn(
          'bg-card border-r border-border transition-all duration-300 flex flex-col',
          sidebarOpen ? 'w-96' : 'w-0',
          'lg:w-96 lg:block',
          !sidebarOpen && 'lg:w-0 lg:hidden'
        )}>
          {sidebarOpen && (
            <div className="flex flex-col h-full">
              {/* 标签切换 */}
              <div className="flex border-b border-border">
                <button
                  onClick={() => setActiveTab('channels')}
                  className={cn(
                    'flex-1 px-4 py-3 text-sm font-medium transition-colors',
                    activeTab === 'channels'
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground'
                  )}
                >
                  频道列表
                </button>
                <button
                  onClick={() => setActiveTab('sources')}
                  className={cn(
                    'flex-1 px-4 py-3 text-sm font-medium transition-colors',
                    activeTab === 'sources'
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground'
                  )}
                >
                  订阅源
                </button>
              </div>

              {/* 内容区域 */}
              <div className="flex-1 overflow-hidden">
                {activeTab === 'channels' ? (
                  <ChannelList
                    channels={channels}
                    onChannelSelect={handleChannelSelect}
                    selectedChannel={selectedChannel}
                    loading={false}
                    error={error}
                  />
                ) : (
                  <SourceManager
                    sources={sources}
                    onSourceAdd={handleSourceAdd}
                    onSourceUpdate={handleSourceUpdate}
                    onSourceDelete={handleSourceDelete}
                    onSourceRefresh={handleSourceRefresh}
                    loading={false}
                  />
                )}
              </div>
            </div>
          )}
        </aside>

        {/* 主内容区域 */}
        <main className="flex-1 flex flex-col">
          {/* 单链接输入区域 */}
          <div className="p-4 border-b border-border bg-card/50">
            <div className="flex flex-col space-y-3">
              <div className="flex items-center space-x-2">
                <Tv className="w-4 h-4 text-primary" />
                <span className="text-sm font-medium">直播链接播放</span>
                {isDirectMode && (
                  <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                    当前模式
                  </span>
                )}
              </div>

              <div className="flex space-x-2">
                <input
                  type="url"
                  value={directUrl}
                  onChange={(e) => setDirectUrl(e.target.value)}
                  placeholder="输入直播链接 (http:// 或 https://)"
                  className="flex-1 px-3 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleDirectUrlPlay()
                    }
                  }}
                />
                <button
                  onClick={handleDirectUrlPlay}
                  disabled={!directUrl.trim()}
                  className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  播放
                </button>
                {isDirectMode && (
                  <button
                    onClick={handleClearDirectUrl}
                    className="px-4 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/90 transition-colors"
                  >
                    清除
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* 播放器区域 */}
          <div className="flex-1 p-4">
            <ErrorBoundary>
              <Player
                channel={selectedChannel}
                onError={handlePlayerError}
                className="w-full h-full"
              />
            </ErrorBoundary>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="mx-4 mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <div className="text-destructive text-sm">{error}</div>
              <button
                onClick={() => setError(null)}
                className="text-xs text-destructive/70 hover:text-destructive mt-1"
              >
                关闭
              </button>
            </div>
          )}
        </main>
      </div>

      {/* 设置面板 */}
      <SettingsPanel
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        autoRefreshEnabled={autoRefreshEnabled}
        refreshInterval={refreshInterval}
        onAutoRefreshChange={setAutoRefreshEnabled}
        onRefreshIntervalChange={setRefreshInterval}
      />

      {/* 移动端遮罩 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  )
}

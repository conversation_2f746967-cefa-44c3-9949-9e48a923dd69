/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/channels/route";
exports.ids = ["app/api/channels/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchannels%2Froute&page=%2Fapi%2Fchannels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchannels%2Froute.ts&appDir=D%3A%5CProject%5CIPTV%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5CIPTV&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchannels%2Froute&page=%2Fapi%2Fchannels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchannels%2Froute.ts&appDir=D%3A%5CProject%5CIPTV%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5CIPTV&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var D_Project_IPTV_src_app_api_channels_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/channels/route.ts */ \"(rsc)/./src/app/api/channels/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/channels/route\",\n        pathname: \"/api/channels\",\n        filename: \"route\",\n        bundlePath: \"app/api/channels/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"D:\\\\Project\\\\IPTV\\\\src\\\\app\\\\api\\\\channels\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Project_IPTV_src_app_api_channels_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/channels/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchannels%2Froute&page=%2Fapi%2Fchannels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchannels%2Froute.ts&appDir=D%3A%5CProject%5CIPTV%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5CIPTV&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/channels/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/channels/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n\n\n// GET /api/channels - 获取频道列表\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        // 解析查询参数\n        const filters = {\n            query: searchParams.get('query') || undefined,\n            group: searchParams.get('group') || undefined,\n            source: searchParams.get('source') || undefined,\n            sortBy: searchParams.get('sortBy') || 'name',\n            sortOrder: searchParams.get('sortOrder') || 'asc',\n            page: parseInt(searchParams.get('page') || '1'),\n            limit: parseInt(searchParams.get('limit') || '10000')\n        };\n        // 获取频道数据\n        const sourceId = filters.source;\n        const groupName = filters.group;\n        const dbChannels = await _lib_database__WEBPACK_IMPORTED_MODULE_1__.DatabaseOperations.getChannels(sourceId, groupName);\n        // 转换数据库格式到API格式\n        let channels = dbChannels.map((channel)=>({\n                id: channel.id,\n                name: channel.name,\n                url: channel.url,\n                group: channel.group_name,\n                logo: channel.logo_url || undefined,\n                sourceId: channel.source_id,\n                createdAt: new Date(channel.created_at),\n                updatedAt: new Date(channel.updated_at)\n            }));\n        // 应用搜索过滤\n        if (filters.query) {\n            const query = filters.query.toLowerCase();\n            channels = channels.filter((channel)=>channel.name.toLowerCase().includes(query) || channel.group.toLowerCase().includes(query));\n        }\n        // 应用排序\n        channels.sort((a, b)=>{\n            let comparison = 0;\n            switch(filters.sortBy){\n                case 'name':\n                    comparison = a.name.localeCompare(b.name);\n                    break;\n                case 'group':\n                    comparison = a.group.localeCompare(b.group) || a.name.localeCompare(b.name);\n                    break;\n                case 'createdAt':\n                    comparison = a.createdAt.getTime() - b.createdAt.getTime();\n                    break;\n                default:\n                    comparison = 0;\n            }\n            return filters.sortOrder === 'desc' ? -comparison : comparison;\n        });\n        // 应用分页\n        const startIndex = (filters.page - 1) * filters.limit;\n        const endIndex = startIndex + filters.limit;\n        const paginatedChannels = channels.slice(startIndex, endIndex);\n        // 构建响应\n        const response = {\n            success: true,\n            data: {\n                channels: paginatedChannels,\n                pagination: {\n                    page: filters.page,\n                    limit: filters.limit,\n                    total: channels.length,\n                    totalPages: Math.ceil(channels.length / filters.limit)\n                },\n                filters\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('Failed to get channels:', error);\n        const response = {\n            success: false,\n            error: 'Failed to get channels',\n            message: error instanceof Error ? error.message : 'Unknown error'\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 500\n        });\n    }\n}\n// POST /api/channels - 添加单个频道\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { name, url, group, logo, sourceId } = body;\n        // 验证必需字段\n        if (!name || !url) {\n            const response = {\n                success: false,\n                error: 'Missing required fields',\n                message: 'Name and URL are required'\n            };\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n                status: 400\n            });\n        }\n        // 验证URL格式\n        try {\n            new URL(url);\n        } catch  {\n            const response = {\n                success: false,\n                error: 'Invalid URL',\n                message: 'Please provide a valid URL'\n            };\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n                status: 400\n            });\n        }\n        // 如果指定了sourceId，验证源是否存在\n        if (sourceId) {\n            const source = await _lib_database__WEBPACK_IMPORTED_MODULE_1__.DatabaseOperations.getSource(sourceId);\n            if (!source) {\n                const response = {\n                    success: false,\n                    error: 'Source not found',\n                    message: `Source with id ${sourceId} not found`\n                };\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n                    status: 404\n                });\n            }\n        }\n        // 创建频道\n        const channelId = await _lib_database__WEBPACK_IMPORTED_MODULE_1__.DatabaseOperations.createChannel({\n            name: name.trim(),\n            url: url.trim(),\n            group_name: (group || 'Default').trim(),\n            logo_url: logo?.trim() || null,\n            source_id: sourceId || 'manual'\n        });\n        // 获取创建的频道\n        const createdChannel = await _lib_database__WEBPACK_IMPORTED_MODULE_1__.DatabaseOperations.getChannel(channelId);\n        if (!createdChannel) {\n            throw new Error('Failed to retrieve created channel');\n        }\n        const apiChannel = {\n            id: createdChannel.id,\n            name: createdChannel.name,\n            url: createdChannel.url,\n            group: createdChannel.group_name,\n            logo: createdChannel.logo_url || undefined,\n            sourceId: createdChannel.source_id,\n            createdAt: new Date(createdChannel.created_at),\n            updatedAt: new Date(createdChannel.updated_at)\n        };\n        const response = {\n            success: true,\n            data: apiChannel,\n            message: 'Channel created successfully'\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Failed to create channel:', error);\n        const response = {\n            success: false,\n            error: 'Failed to create channel',\n            message: error instanceof Error ? error.message : 'Unknown error'\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/channels/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseOperations: () => (/* binding */ DatabaseOperations),\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n// 动态导入SQLite3和相关模块\nlet sqlite3 = null;\nlet path = null;\nlet fs = null;\n// 初始化数据库模块\nasync function initDatabaseModules() {\n    if (false) {}\n    if (!sqlite3) {\n        try {\n            sqlite3 = (__webpack_require__(/*! sqlite3 */ \"sqlite3\").verbose)();\n            path = __webpack_require__(/*! path */ \"path\");\n            fs = __webpack_require__(/*! fs */ \"fs\");\n        } catch (error) {\n            console.error('Failed to load database modules:', error);\n            throw new Error('Database modules not available');\n        }\n    }\n}\n// 数据库路径\nconst DB_PATH = process.env.DATABASE_URL || './database/iptv.db';\n// 确保数据库目录存在\nasync function ensureDatabaseDirectory() {\n    await initDatabaseModules();\n    if (path && fs) {\n        const dbDir = path.dirname(DB_PATH);\n        if (!fs.existsSync(dbDir)) {\n            fs.mkdirSync(dbDir, {\n                recursive: true\n            });\n        }\n    }\n}\n// 数据库连接池\nclass DatabasePool {\n    static instance;\n    db = null;\n    constructor(){}\n    static getInstance() {\n        if (!DatabasePool.instance) {\n            DatabasePool.instance = new DatabasePool();\n        }\n        return DatabasePool.instance;\n    }\n    async getConnection() {\n        await initDatabaseModules();\n        await ensureDatabaseDirectory();\n        if (!this.db) {\n            this.db = new sqlite3.Database(DB_PATH, (err)=>{\n                if (err) {\n                    console.error('数据库连接失败:', err.message);\n                    throw err;\n                }\n                console.log('数据库连接成功');\n            });\n            // 启用外键约束\n            this.db.run('PRAGMA foreign_keys = ON');\n            // 初始化数据库表\n            await this.initializeTables();\n        }\n        return this.db;\n    }\n    async initializeTables() {\n        if (!this.db) return;\n        const tables = [\n            // 订阅源表\n            `CREATE TABLE IF NOT EXISTS sources (\n        id TEXT PRIMARY KEY,\n        name TEXT NOT NULL,\n        url TEXT NOT NULL,\n        type TEXT CHECK(type IN ('M3U', 'M3U8', 'URL')) NOT NULL,\n        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,\n        channel_count INTEGER DEFAULT 0,\n        status TEXT CHECK(status IN ('active', 'inactive', 'error')) DEFAULT 'active',\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n      )`,\n            // 频道表\n            `CREATE TABLE IF NOT EXISTS channels (\n        id TEXT PRIMARY KEY,\n        name TEXT NOT NULL,\n        url TEXT NOT NULL,\n        group_name TEXT DEFAULT 'Default',\n        logo_url TEXT,\n        source_id TEXT,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (source_id) REFERENCES sources (id) ON DELETE CASCADE\n      )`,\n            // 用户设置表\n            `CREATE TABLE IF NOT EXISTS settings (\n        key TEXT PRIMARY KEY,\n        value TEXT NOT NULL,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n      )`,\n            // 播放历史表\n            `CREATE TABLE IF NOT EXISTS play_history (\n        id TEXT PRIMARY KEY,\n        channel_id TEXT,\n        channel_name TEXT NOT NULL,\n        played_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        duration INTEGER DEFAULT 0,\n        position INTEGER DEFAULT 0,\n        FOREIGN KEY (channel_id) REFERENCES channels (id) ON DELETE CASCADE\n      )`,\n            // 收藏表\n            `CREATE TABLE IF NOT EXISTS favorites (\n        id TEXT PRIMARY KEY,\n        channel_id TEXT,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (channel_id) REFERENCES channels (id) ON DELETE CASCADE\n      )`,\n            // 索引\n            `CREATE INDEX IF NOT EXISTS idx_channels_source_id ON channels (source_id)`,\n            `CREATE INDEX IF NOT EXISTS idx_channels_group_name ON channels (group_name)`,\n            `CREATE INDEX IF NOT EXISTS idx_play_history_channel_id ON play_history (channel_id)`,\n            `CREATE INDEX IF NOT EXISTS idx_play_history_played_at ON play_history (played_at)`,\n            `CREATE INDEX IF NOT EXISTS idx_favorites_channel_id ON favorites (channel_id)`\n        ];\n        for (const sql of tables){\n            await this.run(sql);\n        }\n    }\n    async run(sql, params = []) {\n        const db = await this.getConnection();\n        return new Promise((resolve, reject)=>{\n            db.run(sql, params, function(err) {\n                if (err) {\n                    reject(err);\n                } else {\n                    resolve();\n                }\n            });\n        });\n    }\n    async get(sql, params = []) {\n        const db = await this.getConnection();\n        return new Promise((resolve, reject)=>{\n            db.get(sql, params, (err, row)=>{\n                if (err) {\n                    reject(err);\n                } else {\n                    resolve(row);\n                }\n            });\n        });\n    }\n    async all(sql, params = []) {\n        const db = await this.getConnection();\n        return new Promise((resolve, reject)=>{\n            db.all(sql, params, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                } else {\n                    resolve(rows);\n                }\n            });\n        });\n    }\n    async close() {\n        if (this.db) {\n            return new Promise((resolve, reject)=>{\n                this.db.close((err)=>{\n                    if (err) {\n                        reject(err);\n                    } else {\n                        this.db = null;\n                        resolve();\n                    }\n                });\n            });\n        }\n    }\n    // 测试数据库连接\n    async testConnection() {\n        try {\n            const db = await this.getConnection();\n            return new Promise((resolve)=>{\n                db.get('SELECT 1', (err)=>{\n                    resolve(!err);\n                });\n            });\n        } catch (error) {\n            console.error('Database connection test failed:', error);\n            return false;\n        }\n    }\n}\n// 导出数据库实例\nconst db = DatabasePool.getInstance();\n// 数据库操作函数\nclass DatabaseOperations {\n    // 源操作\n    static async createSource(source) {\n        const id = `src_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        await db.run(`INSERT INTO sources (id, name, url, type, last_updated, channel_count, status) \n       VALUES (?, ?, ?, ?, ?, ?, ?)`, [\n            id,\n            source.name,\n            source.url,\n            source.type,\n            source.last_updated,\n            source.channel_count,\n            source.status\n        ]);\n        return id;\n    }\n    static async getSources() {\n        return await db.all('SELECT * FROM sources ORDER BY created_at DESC');\n    }\n    static async getSource(id) {\n        return await db.get('SELECT * FROM sources WHERE id = ?', [\n            id\n        ]);\n    }\n    static async updateSource(id, updates) {\n        const fields = Object.keys(updates).map((key)=>`${key} = ?`).join(', ');\n        const values = Object.values(updates);\n        await db.run(`UPDATE sources SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`, [\n            ...values,\n            id\n        ]);\n    }\n    static async deleteSource(id) {\n        await db.run('DELETE FROM sources WHERE id = ?', [\n            id\n        ]);\n    }\n    // 频道操作\n    static async createChannel(channel) {\n        const id = `ch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        await db.run(`INSERT INTO channels (id, name, url, group_name, logo_url, source_id) \n       VALUES (?, ?, ?, ?, ?, ?)`, [\n            id,\n            channel.name,\n            channel.url,\n            channel.group_name,\n            channel.logo_url,\n            channel.source_id\n        ]);\n        return id;\n    }\n    static async getChannels(sourceId, groupName) {\n        let sql = 'SELECT * FROM channels';\n        const params = [];\n        if (sourceId || groupName) {\n            sql += ' WHERE';\n            const conditions = [];\n            if (sourceId) {\n                conditions.push(' source_id = ?');\n                params.push(sourceId);\n            }\n            if (groupName) {\n                conditions.push(' group_name = ?');\n                params.push(groupName);\n            }\n            sql += conditions.join(' AND');\n        }\n        sql += ' ORDER BY group_name, name';\n        return await db.all(sql, params);\n    }\n    static async getChannel(id) {\n        return await db.get('SELECT * FROM channels WHERE id = ?', [\n            id\n        ]);\n    }\n    static async updateChannel(id, updates) {\n        const fields = Object.keys(updates).map((key)=>`${key} = ?`).join(', ');\n        const values = Object.values(updates);\n        await db.run(`UPDATE channels SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`, [\n            ...values,\n            id\n        ]);\n    }\n    static async deleteChannel(id) {\n        await db.run('DELETE FROM channels WHERE id = ?', [\n            id\n        ]);\n    }\n    static async deleteChannelsBySource(sourceId) {\n        await db.run('DELETE FROM channels WHERE source_id = ?', [\n            sourceId\n        ]);\n    }\n    // 设置操作\n    static async getSetting(key) {\n        const result = await db.get('SELECT value FROM settings WHERE key = ?', [\n            key\n        ]);\n        return result?.value;\n    }\n    static async setSetting(key, value) {\n        await db.run(`INSERT OR REPLACE INTO settings (key, value, updated_at) \n       VALUES (?, ?, CURRENT_TIMESTAMP)`, [\n            key,\n            value\n        ]);\n    }\n    static async getSettings() {\n        const rows = await db.all('SELECT key, value FROM settings');\n        return rows.reduce((acc, row)=>{\n            acc[row.key] = row.value;\n            return acc;\n        }, {});\n    }\n    // 测试数据库连接\n    static async testConnection() {\n        return await db.testConnection();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "sqlite3":
/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("sqlite3");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchannels%2Froute&page=%2Fapi%2Fchannels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchannels%2Froute.ts&appDir=D%3A%5CProject%5CIPTV%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5CIPTV&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();
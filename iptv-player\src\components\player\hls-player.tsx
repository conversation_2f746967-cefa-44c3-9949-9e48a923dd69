"use client"

import Hls from 'hls.js';
import { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './error-handler';

interface HlsPlayerProps {
  src: string;
  onError?: (error: any) => void;
  onReady?: () => void;
  onPlay?: () => void;
  onPause?: () => void;
  onTimeUpdate?: (currentTime: number) => void;
  onQualityChange?: (levels: any[], currentLevel: number) => void;
  className?: string;
  autoPlay?: boolean;
  muted?: boolean;
}

export interface HlsPlayerRef {
  play: () => Promise<void>;
  pause: () => void;
  getCurrentTime: () => number;
  getDuration: () => number;
  setCurrentTime: (time: number) => void;
  getVolume: () => number;
  setVolume: (volume: number) => void;
  toggleMute: () => void;
  isMuted: () => boolean;
  requestFullscreen: () => Promise<void>;
  exitFullscreen: () => Promise<void>;
  isFullscreen: () => boolean;
  getQualityLevels: () => any[];
  setQualityLevel: (level: number) => void;
  getCurrentQualityLevel: () => number;
  destroy: () => void;
}

const HlsPlayer = forwardRef<HlsPlayerRef, HlsPlayerProps>(({
  src,
  onError,
  onReady,
  onPlay,
  onPause,
  onTimeUpdate,
  onQualityChange,
  className = "w-full h-full",
  autoPlay = false,
  muted = true
}, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<Hls | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [volume, setVolumeState] = useState(1);
  const [isMutedState, setIsMutedState] = useState(muted);
  const [isFullscreenState, setIsFullscreenState] = useState(false);
  const [qualityLevels, setQualityLevels] = useState<any[]>([]);
  const [currentQualityLevel, setCurrentQualityLevel] = useState(-1);

  // 暴露播放器控制方法
  useImperativeHandle(ref, () => ({
    play: async () => {
      if (videoRef.current) {
        try {
          await videoRef.current.play();
        } catch (err) {
          console.error('Play failed:', err);
          throw err;
        }
      }
    },
    pause: () => {
      if (videoRef.current) {
        videoRef.current.pause();
      }
    },
    getCurrentTime: () => {
      return videoRef.current?.currentTime || 0;
    },
    getDuration: () => {
      return videoRef.current?.duration || 0;
    },
    setCurrentTime: (time: number) => {
      if (videoRef.current) {
        videoRef.current.currentTime = time;
      }
    },
    getVolume: () => {
      return videoRef.current?.volume || 0;
    },
    setVolume: (volume: number) => {
      if (videoRef.current) {
        const newVolume = Math.max(0, Math.min(1, volume));
        videoRef.current.volume = newVolume;
        setVolumeState(newVolume);
      }
    },
    toggleMute: () => {
      if (videoRef.current) {
        const newMuted = !videoRef.current.muted;
        videoRef.current.muted = newMuted;
        setIsMutedState(newMuted);
      }
    },
    isMuted: () => {
      return videoRef.current?.muted || false;
    },
    requestFullscreen: async () => {
      if (videoRef.current && videoRef.current.requestFullscreen) {
        try {
          await videoRef.current.requestFullscreen();
          setIsFullscreenState(true);
        } catch (err) {
          console.error('Fullscreen request failed:', err);
        }
      }
    },
    exitFullscreen: async () => {
      if (document.exitFullscreen) {
        try {
          await document.exitFullscreen();
          setIsFullscreenState(false);
        } catch (err) {
          console.error('Exit fullscreen failed:', err);
        }
      }
    },
    isFullscreen: () => {
      return isFullscreenState;
    },
    getQualityLevels: () => {
      return qualityLevels;
    },
    setQualityLevel: (level: number) => {
      if (hlsRef.current) {
        hlsRef.current.currentLevel = level;
        setCurrentQualityLevel(level);
        console.log(`🎯 画质切换到: ${level === -1 ? '自动' : qualityLevels[level]?.height + 'p'}`);
      }
    },
    getCurrentQualityLevel: () => {
      return currentQualityLevel;
    },
    destroy: () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    }
  }));

  // URL可访问性预检测
  const testUrlAccessibility = async (url: string): Promise<{ accessible: boolean; error?: string; details?: string }> => {
    try {
      console.log('🔍 Testing URL accessibility:', url);
      const response = await fetch(url, {
        method: 'HEAD',
        mode: 'cors',
        cache: 'no-cache'
      });

      if (response.ok) {
        console.log('✅ URL is accessible');
        return { accessible: true };
      } else {
        console.log('❌ URL returned error status:', response.status);
        return {
          accessible: false,
          error: `HTTP ${response.status}`,
          details: `服务器返回 ${response.status} 错误`
        };
      }
    } catch (error: any) {
      console.log('❌ URL accessibility test failed:', error);

      if (error.name === 'TypeError' && error.message.includes('CORS')) {
        return {
          accessible: false,
          error: 'CORS错误',
          details: '跨域访问被阻止，可能需要代理服务'
        };
      } else if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        return {
          accessible: false,
          error: '网络错误',
          details: '无法连接到服务器，请检查网络连接'
        };
      } else {
        return {
          accessible: false,
          error: '连接失败',
          details: error.message || '未知网络错误'
        };
      }
    }
  };

  useEffect(() => {
    const video = videoRef.current;
    if (!video || !src) return;

    console.log('🎬 Initializing HLS player with source:', src);
    setIsLoading(true);
    setError(null);

    // 清理之前的实例
    if (hlsRef.current) {
      hlsRef.current.destroy();
      hlsRef.current = null;
    }

    // 检查文件类型 - 支持更多格式
    const isHlsStream = src.includes('.m3u8') || src.includes('m3u8') || src.includes('playlist');
    const isDirectVideo = src.includes('.mp4') || src.includes('mp4') ||
                         src.includes('.webm') || src.includes('webm') ||
                         src.includes('.avi') || src.includes('avi') ||
                         src.includes('.mov') || src.includes('mov') ||
                         src.includes('.mkv') || src.includes('mkv');

    if (isDirectVideo) {
      // 直接播放视频文件
      const videoType = src.includes('.mp4') ? 'MP4' :
                       src.includes('.webm') ? 'WebM' :
                       src.includes('.avi') ? 'AVI' :
                       src.includes('.mov') ? 'MOV' :
                       src.includes('.mkv') ? 'MKV' : '视频';
      console.log(`📹 ${videoType} file detected, using native video player`);

      // 预检测URL可访问性
      testUrlAccessibility(src).then(result => {
        if (!result.accessible) {
          console.error('❌ URL accessibility test failed:', result);
          setError(`${result.error}: ${result.details}`);
          setIsLoading(false);
          onError?.({
            type: 'NETWORK_ERROR',
            message: result.error || '网络错误',
            details: result.details || 'URL不可访问',
            src: src
          });
          return;
        }

        // URL可访问，继续播放
        console.log('✅ URL accessibility confirmed, starting playback');
        video.src = src;
      }).catch(error => {
        console.error('❌ URL accessibility test error:', error);
        // 即使预检测失败，也尝试直接播放
        video.src = src;
      });

      const handleLoadedMetadata = () => {
        console.log(`📺 ${videoType} loaded successfully`);
        setIsLoading(false);
        onReady?.();
      };

      const handleError = (e: Event) => {
        const video = e.target as HTMLVideoElement;
        const mediaError = video.error;
        let errorMessage = `${videoType}播放失败`;
        let errorDetails = '';

        if (mediaError) {
          switch (mediaError.code) {
            case MediaError.MEDIA_ERR_ABORTED:
              errorMessage = '播放被中止';
              errorDetails = '用户中止了视频播放';
              break;
            case MediaError.MEDIA_ERR_NETWORK:
              errorMessage = '网络错误';
              errorDetails = '网络连接问题导致视频下载失败';
              break;
            case MediaError.MEDIA_ERR_DECODE:
              errorMessage = '解码错误';
              errorDetails = '视频文件损坏或格式不支持';
              break;
            case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
              errorMessage = '格式不支持';
              errorDetails = '浏览器不支持该视频格式或MIME类型';
              break;
            default:
              errorMessage = '未知播放错误';
              errorDetails = `错误代码: ${mediaError.code}`;
          }
        }

        console.error(`❌ ${videoType} playback error:`, {
          event: e,
          mediaError,
          code: mediaError?.code,
          message: mediaError?.message,
          src: video.src
        });

        setError(`${errorMessage}: ${errorDetails}`);
        onError?.({
          type: 'MEDIA_ERROR',
          message: errorMessage,
          details: errorDetails,
          code: mediaError?.code,
          src: video.src
        });
      };

      video.addEventListener('loadedmetadata', handleLoadedMetadata);
      video.addEventListener('error', handleError);

      return () => {
        video.removeEventListener('loadedmetadata', handleLoadedMetadata);
        video.removeEventListener('error', handleError);
      };

    } else if (isHlsStream && Hls.isSupported()) {
      // 使用HLS.js处理HLS流
      console.log('✅ HLS stream detected, using HLS.js');

      // 创建HLS.js实例，使用性能优化配置
      const hls = new Hls({
        debug: false,
        enableWorker: true,
        lowLatencyMode: true,
        // 优化缓冲区设置，减少初始加载时间
        backBufferLength: 30,        // 减少后缓冲区长度
        maxBufferLength: 15,         // 减少最大缓冲区长度，加快启动
        maxBufferSize: 30 * 1000 * 1000, // 减少到30MB，降低内存使用
        maxBufferHole: 0.3,          // 减少缓冲区空洞容忍度
        highBufferWatchdogPeriod: 1, // 更频繁的缓冲区监控
        nudgeOffset: 0.05,           // 减少nudge偏移
        nudgeMaxRetry: 2,            // 减少重试次数
        maxMaxBufferLength: 300,     // 减少最大缓冲区长度
        startLevel: -1,              // 自动选择起始质量
        capLevelToPlayerSize: true,
        // 优化加载超时设置，加快启动速度
        fragLoadingTimeOut: 10000,   // 减少片段加载超时
        manifestLoadingTimeOut: 5000, // 减少清单加载超时
        levelLoadingTimeOut: 5000,   // 减少级别加载超时
        fragLoadingMaxRetry: 3,      // 减少片段重试次数
        levelLoadingMaxRetry: 2,     // 减少级别重试次数
        manifestLoadingMaxRetry: 1,  // 减少清单重试次数
        // 启动优化
        startFragPrefetch: true,     // 启用片段预取
        testBandwidth: false,        // 禁用带宽测试，加快启动
      });

      hlsRef.current = hls;

      // 事件监听
      hls.on(Hls.Events.MEDIA_ATTACHED, () => {
        console.log('📺 Media attached to HLS instance');
      });

      hls.on(Hls.Events.MANIFEST_PARSED, (event, data) => {
        console.log('📋 Manifest parsed, levels:', data.levels.length);
        setQualityLevels(data.levels);
        setCurrentQualityLevel(hls.currentLevel);
        setIsLoading(false);
        onReady?.();
        onQualityChange?.(data.levels, hls.currentLevel);
      });

      hls.on(Hls.Events.LEVEL_SWITCHED, (event, data) => {
        console.log('🔄 Level switched to:', data.level);
        setCurrentQualityLevel(data.level);
        onQualityChange?.(qualityLevels, data.level);
      });

      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('❌ HLS Error:', data);
        
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('🔄 Network error, trying to recover...');
              hls.startLoad();
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('🔄 Media error, trying to recover...');
              hls.recoverMediaError();
              break;
            default:
              console.log('💥 Fatal error, destroying HLS instance');
              hls.destroy();
              setError(`播放错误: ${data.details}`);
              onError?.(data);
              break;
          }
        }
      });

      // 加载源并附加到视频元素
      hls.loadSource(src);
      hls.attachMedia(video);

    } else if (isHlsStream && video.canPlayType('application/vnd.apple.mpegurl')) {
      // Safari原生HLS支持
      console.log('🍎 Using native HLS support (Safari)');
      video.src = src;

      const handleLoadedMetadata = () => {
        console.log('📺 Native HLS loaded');
        setIsLoading(false);
        onReady?.();
      };

      const handleError = (e: Event) => {
        console.error('❌ Native HLS error:', e);
        setError('播放错误: 视频加载失败');
        onError?.(e);
      };

      video.addEventListener('loadedmetadata', handleLoadedMetadata);
      video.addEventListener('error', handleError);

      return () => {
        video.removeEventListener('loadedmetadata', handleLoadedMetadata);
        video.removeEventListener('error', handleError);
      };

    } else {
      // 尝试直接播放（可能是其他格式的视频文件）
      console.log('🎥 Attempting direct video playback');
      video.src = src;

      const handleLoadedMetadata = () => {
        console.log('📺 Direct video loaded');
        setIsLoading(false);
        onReady?.();
      };

      const handleError = (e: Event) => {
        console.error('❌ Direct video error:', e);
        setError('播放错误: 视频格式不支持或无法访问');
        onError?.(e);
      };

      video.addEventListener('loadedmetadata', handleLoadedMetadata);
      video.addEventListener('error', handleError);

      return () => {
        video.removeEventListener('loadedmetadata', handleLoadedMetadata);
        video.removeEventListener('error', handleError);
      };
    }

    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    };
  }, [src]);

  // 全屏状态监听
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isFullscreen = !!document.fullscreenElement;
      setIsFullscreenState(isFullscreen);
      console.log(isFullscreen ? '🔲 进入全屏模式' : '🔳 退出全屏模式');
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 视频事件监听
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handlePlay = () => {
      console.log('▶️ Video playing');
      onPlay?.();
    };

    const handlePause = () => {
      console.log('⏸️ Video paused');
      onPause?.();
    };

    const handleTimeUpdate = () => {
      onTimeUpdate?.(video.currentTime);
    };

    const handleLoadStart = () => {
      console.log('🔄 Video load start');
      setIsLoading(true);
    };

    const handleCanPlay = () => {
      console.log('✅ Video can play');
      setIsLoading(false);
    };

    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('loadstart', handleLoadStart);
    video.addEventListener('canplay', handleCanPlay);

    return () => {
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('loadstart', handleLoadStart);
      video.removeEventListener('canplay', handleCanPlay);
    };
  }, [onPlay, onPause, onTimeUpdate]);

  return (
    <div className={`relative ${className}`}>
      <video
        ref={videoRef}
        className="w-full h-full bg-black"
        controls
        playsInline
        autoPlay={autoPlay}
        muted={muted}
        preload="none"
      />
      
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 text-white transition-opacity duration-300">
          <div className="text-center">
            <div className="animate-pulse w-6 h-6 bg-white/60 rounded-full mx-auto mb-2"></div>
            <div className="text-sm opacity-80">正在加载...</div>
          </div>
        </div>
      )}

      {error && (
        <ErrorHandler
          error={error}
          onRetry={() => {
            setError(null);
            setIsLoading(true);
            // 触发重新加载
            if (videoRef.current) {
              videoRef.current.load();
            }
          }}
          onClose={() => setError(null)}
        />
      )}
    </div>
  );
});

HlsPlayer.displayName = 'HlsPlayer';

export default HlsPlayer;

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const subscriptionId = searchParams.get('subscriptionId')
    const group = searchParams.get('group')
    const search = searchParams.get('search')
    const favorites = searchParams.get('favorites')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '1000') // 增加默认限制到1000
    const skip = (page - 1) * limit

    const where: Record<string, unknown> = {}

    if (subscriptionId) {
      where.subscriptionId = subscriptionId
    }

    if (group) {
      where.group = group
    }

    if (search) {
      // SQLite兼容的大小写不敏感搜索
      where.name = {
        contains: search
      }
    }

    if (favorites === 'true') {
      where.isFavorite = true
    }

    // 获取总数
    const total = await prisma.channel.count({ where })

    // 获取分页数据
    const channels = await prisma.channel.findMany({
      where,
      include: {
        subscription: {
          select: {
            name: true,
            isActive: true
          }
        }
      },
      orderBy: [
        { isFavorite: 'desc' },
        { name: 'asc' }
      ],
      skip,
      take: limit
    })

    return NextResponse.json({
      channels,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching channels:', error)
    return NextResponse.json(
      { error: 'Failed to fetch channels' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, url, logo, group, subscriptionId } = body

    if (!name || !url || !subscriptionId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const channel = await prisma.channel.create({
      data: {
        name,
        url,
        logo,
        group,
        subscriptionId
      }
    })

    return NextResponse.json(channel, { status: 201 })
  } catch (error) {
    console.error('Error creating channel:', error)
    return NextResponse.json(
      { error: 'Failed to create channel' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, isFavorite } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Channel ID is required' },
        { status: 400 }
      )
    }

    const channel = await prisma.channel.update({
      where: { id },
      data: { isFavorite }
    })

    return NextResponse.json(channel)
  } catch (error) {
    console.error('Error updating channel:', error)
    return NextResponse.json(
      { error: 'Failed to update channel' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Channel ID is required' },
        { status: 400 }
      )
    }

    await prisma.channel.delete({
      where: { id }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting channel:', error)
    return NextResponse.json(
      { error: 'Failed to delete channel' },
      { status: 500 }
    )
  }
}

"use client"

import { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Tv, Moon, Sun, Play } from 'lucide-react'
import { useTheme } from 'next-themes'
import ChannelList from '@/components/channels/channel-list'
import SubscriptionManager from '@/components/subscriptions/subscription-manager'
import TestChannels from '@/components/test-channels'

// 动态导入播放器组件以避免SSR问题
const VideoPlayer = dynamic(() => import('@/components/player/video-player'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full bg-black rounded-lg flex items-center justify-center">
      <div className="text-white">加载播放器中...</div>
    </div>
  )
})



interface Channel {
  id: string
  name: string
  url: string
  logo?: string | null
  group?: string | null
  isFavorite: boolean
}

interface Subscription {
  id: string
  name: string
  url: string
  type: string
  isActive: boolean
  lastUpdated: Date
  channelCount?: number
}

export default function Home() {
  const { theme, setTheme } = useTheme()
  const [channels, setChannels] = useState<Channel[]>([])
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [currentChannel, setCurrentChannel] = useState<Channel | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('player')

  // 获取频道列表
  const fetchChannels = async (retries = 3) => {
    try {
      // 请求所有频道，不限制数量
      const response = await fetch('/api/channels?limit=10000')
      if (response.ok) {
        const result = await response.json()
        const channelData = result.channels || result // 兼容新旧API格式
        setChannels(channelData)
        if (channelData.length > 0 && !currentChannel) {
          // 直接使用原始URL，让HLS.js处理
          const firstChannel = channelData[0]
          setCurrentChannel(firstChannel)
        }
        setError(null)
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Failed to fetch channels:', error)
      if (retries > 0) {
        setTimeout(() => fetchChannels(retries - 1), 2000)
      } else {
        setError('无法加载频道列表，请检查网络连接')
      }
    }
  }

  // 获取订阅源列表
  const fetchSubscriptions = async (retries = 3) => {
    try {
      const response = await fetch('/api/subscriptions')
      if (response.ok) {
        const data = await response.json()
        setSubscriptions(data.map((sub: Subscription & { lastUpdated: string }) => ({
          ...sub,
          lastUpdated: new Date(sub.lastUpdated)
        })))
        setError(null)
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Failed to fetch subscriptions:', error)
      if (retries > 0) {
        setTimeout(() => fetchSubscriptions(retries - 1), 2000)
      } else {
        setError('无法加载订阅源列表，请检查网络连接')
      }
    }
  }

  useEffect(() => {
    let isMounted = true

    const loadData = async () => {
      if (!isMounted) return

      setIsLoading(true)
      setError(null)
      await Promise.all([fetchChannels(), fetchSubscriptions()])

      if (isMounted) {
        setIsLoading(false)
      }
    }

    loadData()

    // 清理函数 - 防止组件卸载后的状态更新
    return () => {
      isMounted = false
    }
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  // 处理频道选择
  const handleChannelSelect = (channel: Channel) => {
    // 直接使用原始URL，让HLS.js处理跨域和流媒体
    console.log('Selected channel:', channel.name, 'URL:', channel.url)
    setCurrentChannel(channel)
    setActiveTab('player')
  }

  // 处理收藏切换
  const handleToggleFavorite = async (channelId: string) => {
    try {
      const channel = channels.find(c => c.id === channelId)
      if (!channel) return

      const response = await fetch('/api/channels', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: channelId,
          isFavorite: !channel.isFavorite
        })
      })

      if (response.ok) {
        setChannels(prev => prev.map(c =>
          c.id === channelId ? { ...c, isFavorite: !c.isFavorite } : c
        ))
      }
    } catch (error) {
      console.error('Failed to toggle favorite:', error)
    }
  }

  // 处理订阅源操作
  const handleAddSubscription = async (subscription: Omit<Subscription, 'id' | 'lastUpdated'>) => {
    try {
      const response = await fetch('/api/subscriptions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(subscription)
      })

      if (response.ok) {
        await fetchSubscriptions()
        await fetchChannels()
      }
    } catch (error) {
      console.error('Failed to add subscription:', error)
      throw error
    }
  }

  const handleDeleteSubscription = async (id: string) => {
    try {
      const response = await fetch(`/api/subscriptions?id=${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        await fetchSubscriptions()
        await fetchChannels()
      }
    } catch (error) {
      console.error('Failed to delete subscription:', error)
      throw error
    }
  }

  const handleUpdateSubscription = async (id: string) => {
    try {
      const response = await fetch(`/api/subscriptions/${id}/update`, {
        method: 'POST'
      })

      if (response.ok) {
        await fetchSubscriptions()
        await fetchChannels()
      }
    } catch (error) {
      console.error('Failed to update subscription:', error)
      throw error
    }
  }

  const handleToggleSubscriptionActive = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch('/api/subscriptions', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id, isActive })
      })

      if (response.ok) {
        await fetchSubscriptions()
      }
    } catch (error) {
      console.error('Failed to toggle subscription:', error)
      throw error
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>加载中...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold mb-2">加载失败</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            重新加载
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 头部 */}
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Tv className="h-6 w-6" />
              <h1 className="text-xl font-bold">IPTV Player</h1>
              {currentChannel && (
                <Badge variant="outline" className="ml-4">
                  正在播放: {currentChannel.name}
                </Badge>
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            >
              {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </header>

      {/* 主内容 */}
      <div className="container mx-auto px-4 py-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="player">播放器</TabsTrigger>
            <TabsTrigger value="channels">频道列表</TabsTrigger>
            <TabsTrigger value="subscriptions">订阅管理</TabsTrigger>
            <TabsTrigger value="test">测试频道</TabsTrigger>
          </TabsList>

          <TabsContent value="player" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* 播放器区域 */}
              <div className="lg:col-span-3">
                <Card className="h-[800px]">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>{currentChannel?.name || '请选择频道'}</span>
                      {currentChannel?.group && (
                        <Badge variant="secondary">{currentChannel.group}</Badge>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="h-[720px]">
                    <div className="w-full h-full bg-black rounded-lg overflow-hidden">
                      {currentChannel ? (
                        <VideoPlayer
                          channel={currentChannel}
                          onError={(error) => {
                            console.error('播放器错误:', error)
                            setError(error)
                          }}
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-white">
                          <div className="text-center">
                            <Play className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>请从右侧频道列表选择要播放的频道</p>
                            <p className="text-sm text-gray-400 mt-2">
                              频道总数: {channels.length}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 频道列表侧边栏 */}
              <div className="lg:col-span-1">
                <Card className="h-[800px]">
                  <CardHeader>
                    <CardTitle>频道列表</CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <ChannelList
                      channels={channels}
                      currentChannelId={currentChannel?.id}
                      onChannelSelect={handleChannelSelect}
                      onToggleFavorite={handleToggleFavorite}
                      className="h-[720px]"
                    />
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="channels">
            <Card>
              <CardHeader>
                <CardTitle>所有频道</CardTitle>
              </CardHeader>
              <CardContent>
                <ChannelList
                  channels={channels}
                  currentChannelId={currentChannel?.id}
                  onChannelSelect={handleChannelSelect}
                  onToggleFavorite={handleToggleFavorite}
                  className="h-[800px]"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="subscriptions">
            <SubscriptionManager
              subscriptions={subscriptions}
              onAdd={handleAddSubscription}
              onDelete={handleDeleteSubscription}
              onUpdate={handleUpdateSubscription}
              onToggleActive={handleToggleSubscriptionActive}
            />
          </TabsContent>

          <TabsContent value="test">
            <div className="space-y-6">
              <TestChannels
                onChannelSelect={handleChannelSelect}
                currentChannel={currentChannel}
              />

              {/* 测试说明 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <span>🔬</span>
                    <span>播放器测试说明</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <h4 className="font-semibold text-blue-600 dark:text-blue-400">🎯 测试目标</h4>
                      <ul className="text-sm space-y-1 text-gray-600 dark:text-gray-400">
                        <li>• 验证 HLS.js 播放器功能</li>
                        <li>• 测试多种视频格式支持</li>
                        <li>• 检查错误处理机制</li>
                        <li>• 验证自适应码率功能</li>
                      </ul>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-semibold text-green-600 dark:text-green-400">✅ 新功能特性</h4>
                      <ul className="text-sm space-y-1 text-gray-600 dark:text-gray-400">
                        <li>• 智能格式检测和播放</li>
                        <li>• 音量控制和静音功能</li>
                        <li>• 全屏播放支持</li>
                        <li>• 画质选择（HLS流）</li>
                        <li>• 详细的调试信息</li>
                        <li>• 友好的错误提示</li>
                      </ul>
                    </div>
                  </div>

                  <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                    <div className="flex items-start gap-2">
                      <span className="text-yellow-600 dark:text-yellow-400">⚡</span>
                      <div className="text-sm">
                        <p className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                          重要改进: 从 ArtPlayer 升级到 HLS.js
                        </p>
                        <p className="text-yellow-700 dark:text-yellow-300">
                          新的播放器架构更加简洁高效，支持更多视频格式，提供更好的错误处理和调试体验。
                          HLS.js 是业界标准的流媒体播放解决方案，具有更好的兼容性和稳定性。
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

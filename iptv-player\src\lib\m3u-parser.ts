export interface Channel {
  name: string
  url: string
  logo?: string
  group?: string
}

export interface ParsedM3U {
  channels: Channel[]
  totalChannels: number
}

/**
 * 解析M3U文件内容
 * @param content M3U文件内容
 * @returns 解析后的频道列表
 */
export async function parseM3U(content: string): Promise<ParsedM3U> {
  // 检查内容大小，防止内存溢出
  if (content.length > 50 * 1024 * 1024) { // 50MB限制
    throw new Error('M3U content too large for processing')
  }

  const lines = content.split('\n').map(line => line.trim()).filter(line => line)
  const channels: Channel[] = []

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]

    if (line.startsWith('#EXTINF:')) {
      const nextLine = lines[i + 1]

      if (nextLine && !nextLine.startsWith('#')) {
        const channel = parseExtinf(line, nextLine)
        if (channel) {
          channels.push(channel)
        }
        i++ // 跳过下一行，因为已经处理了
      }
    }
  }

  return {
    channels,
    totalChannels: channels.length
  }
}

/**
 * 解析EXTINF行
 * @param extinfLine EXTINF行内容
 * @param urlLine URL行内容
 * @returns 解析后的频道信息
 */
function parseExtinf(extinfLine: string, urlLine: string): Channel | null {
  try {
    // 移除 #EXTINF: 前缀
    const content = extinfLine.replace('#EXTINF:', '')
    
    // 分离时长和其他信息
    const firstCommaIndex = content.indexOf(',')
    if (firstCommaIndex === -1) return null
    
    const attributesPart = content.substring(0, firstCommaIndex)
    const namePart = content.substring(firstCommaIndex + 1)
    
    // 解析属性
    const attributes = parseAttributes(attributesPart)
    
    // 提取频道名称（移除可能的组信息）
    let channelName = namePart.trim()
    
    // 如果名称中包含组信息，提取实际名称
    const groupMatch = channelName.match(/^(.+?)\s*-\s*(.+)$/)
    if (groupMatch && attributes.group) {
      channelName = groupMatch[2] || groupMatch[1]
    }
    
    return {
      name: channelName || 'Unknown Channel',
      url: urlLine.trim(),
      logo: attributes.logo,
      group: attributes.group
    }
  } catch (error) {
    console.error('Error parsing EXTINF line:', error)
    return null
  }
}

/**
 * 解析属性字符串
 * @param attributesPart 属性部分
 * @returns 解析后的属性对象
 */
function parseAttributes(attributesPart: string): { logo?: string; group?: string } {
  const attributes: { logo?: string; group?: string } = {}
  
  // 解析 tvg-logo
  const logoMatch = attributesPart.match(/tvg-logo="([^"]*)"/)
  if (logoMatch) {
    attributes.logo = logoMatch[1]
  }
  
  // 解析 group-title
  const groupMatch = attributesPart.match(/group-title="([^"]*)"/)
  if (groupMatch) {
    attributes.group = groupMatch[1]
  }
  
  return attributes
}

/**
 * 验证M3U文件格式
 * @param content 文件内容
 * @returns 是否为有效的M3U文件
 */
export function validateM3U(content: string): boolean {
  const lines = content.split('\n').map(line => line.trim())
  
  // 检查是否以 #EXTM3U 开头
  if (!lines[0]?.startsWith('#EXTM3U')) {
    return false
  }
  
  // 检查是否包含至少一个频道
  const hasChannel = lines.some(line => line.startsWith('#EXTINF:'))
  
  return hasChannel
}

/**
 * 从URL获取M3U内容
 * @param url M3U文件URL
 * @param retries 重试次数
 * @returns M3U文件内容
 */
export async function fetchM3U(url: string, retries = 3): Promise<string> {
  const timeout = 30000 // 30秒超时

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      // 创建AbortController用于超时控制
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'application/vnd.apple.mpegurl,application/x-mpegurl,text/plain,*/*'
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // 检查内容长度，防止内存溢出
      const contentLength = response.headers.get('content-length')
      if (contentLength && parseInt(contentLength) > 10 * 1024 * 1024) { // 10MB限制
        throw new Error('M3U file too large (>10MB)')
      }

      const content = await response.text()

      if (!validateM3U(content)) {
        throw new Error('Invalid M3U file format')
      }

      return content
    } catch (error) {
      console.error(`Error fetching M3U (attempt ${attempt}/${retries}):`, error)

      if (attempt === retries) {
        throw error
      }

      // 指数退避重试
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
    }
  }

  throw new Error('Failed to fetch M3U after all retries')
}

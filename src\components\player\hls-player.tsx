import Hls from 'hls.js';
import { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';

interface HlsPlayerProps {
  src: string;
  onError?: (error: any) => void;
  onReady?: () => void;
  onPlay?: () => void;
  onPause?: () => void;
  onTimeUpdate?: (currentTime: number) => void;
  className?: string;
  autoPlay?: boolean;
  muted?: boolean;
}

export interface HlsPlayerRef {
  play: () => Promise<void>;
  pause: () => void;
  getCurrentTime: () => number;
  getDuration: () => number;
  setCurrentTime: (time: number) => void;
  getVolume: () => number;
  setVolume: (volume: number) => void;
  destroy: () => void;
}

const HlsPlayer = forwardRef<HlsPlayerRef, HlsPlayerProps>(({
  src,
  onError,
  onReady,
  onPlay,
  onPause,
  onTimeUpdate,
  className = "w-full h-full",
  autoPlay = false,
  muted = true
}, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<Hls | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 暴露播放器控制方法
  useImperativeHandle(ref, () => ({
    play: async () => {
      if (videoRef.current) {
        try {
          await videoRef.current.play();
        } catch (err) {
          console.error('Play failed:', err);
          throw err;
        }
      }
    },
    pause: () => {
      if (videoRef.current) {
        videoRef.current.pause();
      }
    },
    getCurrentTime: () => {
      return videoRef.current?.currentTime || 0;
    },
    getDuration: () => {
      return videoRef.current?.duration || 0;
    },
    setCurrentTime: (time: number) => {
      if (videoRef.current) {
        videoRef.current.currentTime = time;
      }
    },
    getVolume: () => {
      return videoRef.current?.volume || 0;
    },
    setVolume: (volume: number) => {
      if (videoRef.current) {
        videoRef.current.volume = Math.max(0, Math.min(1, volume));
      }
    },
    destroy: () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    }
  }));

  useEffect(() => {
    const video = videoRef.current;
    if (!video || !src) return;

    console.log('🎬 Initializing HLS player with source:', src);
    setIsLoading(true);
    setError(null);

    // 清理之前的实例
    if (hlsRef.current) {
      hlsRef.current.destroy();
      hlsRef.current = null;
    }

    // 检查是否支持HLS.js
    if (Hls.isSupported()) {
      console.log('✅ HLS.js is supported, creating instance');
      
      // 创建HLS.js实例，使用性能优化配置
      const hls = new Hls({
        debug: false,
        enableWorker: true,
        lowLatencyMode: true,
        // 优化缓冲区设置，减少初始加载时间
        backBufferLength: 30,        // 减少后缓冲区长度
        maxBufferLength: 15,         // 减少最大缓冲区长度，加快启动
        maxBufferSize: 30 * 1000 * 1000, // 减少到30MB，降低内存使用
        maxBufferHole: 0.3,          // 减少缓冲区空洞容忍度
        highBufferWatchdogPeriod: 1, // 更频繁的缓冲区监控
        nudgeOffset: 0.05,           // 减少nudge偏移
        nudgeMaxRetry: 2,            // 减少重试次数
        maxMaxBufferLength: 300,     // 减少最大缓冲区长度
        startLevel: -1,              // 自动选择起始质量
        capLevelToPlayerSize: true,
        // 优化加载超时设置，加快启动速度
        fragLoadingTimeOut: 10000,   // 减少片段加载超时
        manifestLoadingTimeOut: 5000, // 减少清单加载超时
        levelLoadingTimeOut: 5000,   // 减少级别加载超时
        fragLoadingMaxRetry: 3,      // 减少片段重试次数
        levelLoadingMaxRetry: 2,     // 减少级别重试次数
        manifestLoadingMaxRetry: 1,  // 减少清单重试次数
        // 启动优化
        startFragPrefetch: true,     // 启用片段预取
        testBandwidth: false,        // 禁用带宽测试，加快启动
      });

      hlsRef.current = hls;

      // 事件监听
      hls.on(Hls.Events.MEDIA_ATTACHED, () => {
        console.log('📺 Media attached to HLS instance');
      });

      hls.on(Hls.Events.MANIFEST_PARSED, (event, data) => {
        console.log('📋 Manifest parsed, levels:', data.levels.length);
        // 快速隐藏loading状态，提升用户体验
        setIsLoading(false);
        onReady?.();
      });

      hls.on(Hls.Events.LEVEL_SWITCHED, (event, data) => {
        console.log('🔄 Level switched to:', data.level);
      });

      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('❌ HLS Error:', data);
        
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('🔄 Network error, trying to recover...');
              hls.startLoad();
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('🔄 Media error, trying to recover...');
              hls.recoverMediaError();
              break;
            default:
              console.log('💥 Fatal error, destroying HLS instance');
              hls.destroy();
              setError(`播放错误: ${data.details}`);
              onError?.(data);
              break;
          }
        }
      });

      // 加载源并附加到视频元素
      hls.loadSource(src);
      hls.attachMedia(video);

    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // Safari原生HLS支持
      console.log('🍎 Using native HLS support (Safari)');
      video.src = src;
      
      const handleLoadedMetadata = () => {
        console.log('📺 Native HLS loaded');
        setIsLoading(false);
        onReady?.();
      };

      const handleError = (e: Event) => {
        console.error('❌ Native HLS error:', e);
        setError('播放错误: 视频加载失败');
        onError?.(e);
      };

      video.addEventListener('loadedmetadata', handleLoadedMetadata);
      video.addEventListener('error', handleError);

      return () => {
        video.removeEventListener('loadedmetadata', handleLoadedMetadata);
        video.removeEventListener('error', handleError);
      };

    } else {
      // 不支持HLS
      console.error('❌ HLS not supported');
      setError('播放错误: 浏览器不支持HLS播放');
      onError?.({ type: 'UNSUPPORTED', message: 'HLS not supported' });
    }

    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    };
  }, [src]);

  // 视频事件监听
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handlePlay = () => {
      console.log('▶️ Video playing');
      onPlay?.();
    };

    const handlePause = () => {
      console.log('⏸️ Video paused');
      onPause?.();
    };

    const handleTimeUpdate = () => {
      onTimeUpdate?.(video.currentTime);
    };

    const handleLoadStart = () => {
      console.log('🔄 Video load start');
      setIsLoading(true);
    };

    const handleCanPlay = () => {
      console.log('✅ Video can play');
      setIsLoading(false);
    };

    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('loadstart', handleLoadStart);
    video.addEventListener('canplay', handleCanPlay);

    return () => {
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('loadstart', handleLoadStart);
      video.removeEventListener('canplay', handleCanPlay);
    };
  }, [onPlay, onPause, onTimeUpdate]);

  return (
    <div className={`relative ${className}`}>
      <video
        ref={videoRef}
        className="w-full h-full bg-black"
        controls
        playsInline
        autoPlay={autoPlay}
        muted={muted}
        preload="none"
      />
      
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 text-white transition-opacity duration-300">
          <div className="text-center">
            <div className="animate-pulse w-6 h-6 bg-white/60 rounded-full mx-auto mb-2"></div>
            <div className="text-sm opacity-80">正在加载...</div>
          </div>
        </div>
      )}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80 text-white">
          <div className="text-center">
            <div className="text-red-400 mb-2 text-2xl">⚠️</div>
            <div className="mb-4">{error}</div>
            <button 
              onClick={() => {
                setError(null);
                // 触发重新加载
                if (videoRef.current) {
                  videoRef.current.load();
                }
              }}
              className="px-4 py-2 bg-blue-600 rounded hover:bg-blue-700 transition-colors"
            >
              重试播放
            </button>
          </div>
        </div>
      )}
    </div>
  );
});

HlsPlayer.displayName = 'HlsPlayer';

export default HlsPlayer;

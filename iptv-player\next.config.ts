import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // 启用输出文件跟踪以减少Docker镜像大小
  output: 'standalone',

  // 图片优化配置
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: '**',
      },
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },

  // 服务器外部包配置（Next.js 15+ 新配置）
  serverExternalPackages: ['@prisma/client', 'prisma'],
};

export default nextConfig;

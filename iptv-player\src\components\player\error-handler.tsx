"use client"

import { useState } from 'react';

interface ErrorHandlerProps {
  error: string;
  onRetry: () => void;
  onClose: () => void;
  suggestions?: string[];
}

export default function ErrorHandler({ error, onRetry, onClose, suggestions = [] }: ErrorHandlerProps) {
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      await onRetry();
    } finally {
      setIsRetrying(false);
    }
  };

  const getErrorIcon = (error: string) => {
    if (error.includes('网络') || error.includes('Network')) return '🌐';
    if (error.includes('格式') || error.includes('Format')) return '📹';
    if (error.includes('权限') || error.includes('Permission')) return '🔒';
    if (error.includes('超时') || error.includes('Timeout')) return '⏰';
    return '⚠️';
  };

  const getErrorSuggestions = (error: string) => {
    const defaultSuggestions = [];

    if (error.includes('网络') || error.includes('Network') || error.includes('HTTP')) {
      defaultSuggestions.push(
        '检查网络连接是否正常',
        '尝试刷新页面',
        '检查防火墙设置',
        '尝试使用VPN或代理'
      );
    }

    if (error.includes('格式') || error.includes('Format') || error.includes('解码')) {
      defaultSuggestions.push(
        '该视频格式可能不受支持',
        '尝试选择其他频道',
        '检查视频源是否有效',
        '更新浏览器到最新版本'
      );
    }

    if (error.includes('超时') || error.includes('Timeout')) {
      defaultSuggestions.push(
        '视频源响应较慢，请稍后重试',
        '检查网络速度',
        '尝试选择其他视频源',
        '关闭其他占用带宽的应用'
      );
    }

    if (error.includes('CORS') || error.includes('跨域')) {
      defaultSuggestions.push(
        '视频源存在跨域限制',
        '尝试使用测试频道验证功能',
        '联系管理员配置CORS',
        '使用支持CORS的视频源'
      );
    }

    if (error.includes('404') || error.includes('Not Found')) {
      defaultSuggestions.push(
        '视频文件不存在或已被删除',
        '检查URL是否正确',
        '尝试其他视频源',
        '联系内容提供方'
      );
    }

    if (error.includes('403') || error.includes('Forbidden')) {
      defaultSuggestions.push(
        '访问被拒绝，可能需要认证',
        '检查是否需要登录',
        '尝试其他视频源',
        '联系内容提供方获取访问权限'
      );
    }

    return suggestions.length > 0 ? suggestions : defaultSuggestions;
  };

  const errorSuggestions = getErrorSuggestions(error);

  return (
    <div className="absolute inset-0 flex items-center justify-center bg-black/90 text-white z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-md mx-4 border border-gray-600">
        {/* 错误标题 */}
        <div className="flex items-center gap-3 mb-4">
          <div className="text-3xl">{getErrorIcon(error)}</div>
          <div>
            <h3 className="text-lg font-semibold text-red-400">播放错误</h3>
            <p className="text-sm text-gray-300">视频无法正常播放</p>
          </div>
        </div>

        {/* 错误详情 */}
        <div className="mb-4">
          <div className="bg-gray-900 rounded p-3 text-sm font-mono text-red-300 border-l-4 border-red-500">
            {error}
          </div>
        </div>

        {/* 解决建议 */}
        {errorSuggestions.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-blue-400 mb-2">💡 解决建议:</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              {errorSuggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-blue-400 mt-1">•</span>
                  <span>{suggestion}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex gap-3">
          <button
            onClick={handleRetry}
            disabled={isRetrying}
            className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white px-4 py-2 rounded transition-colors flex items-center justify-center gap-2"
          >
            {isRetrying ? (
              <>
                <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                <span>重试中...</span>
              </>
            ) : (
              <>
                <span>🔄</span>
                <span>重试播放</span>
              </>
            )}
          </button>
          
          <button
            onClick={onClose}
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded transition-colors"
          >
            关闭
          </button>
        </div>

        {/* 技术信息 */}
        <div className="mt-4 pt-4 border-t border-gray-600">
          <details className="text-xs text-gray-400">
            <summary className="cursor-pointer hover:text-gray-300">技术详情</summary>
            <div className="mt-2 bg-gray-900 rounded p-2 font-mono space-y-1">
              <div>时间: {new Date().toLocaleString()}</div>
              <div>浏览器: {navigator.userAgent.split(' ')[0]}</div>
              <div>HLS支持: {typeof window !== 'undefined' && 'MediaSource' in window ? '✅' : '❌'}</div>
              <div>网络状态: {typeof navigator !== 'undefined' && 'onLine' in navigator ? (navigator.onLine ? '🟢 在线' : '🔴 离线') : '❓ 未知'}</div>
              <div>连接类型: {typeof navigator !== 'undefined' && 'connection' in navigator ? (navigator as any).connection?.effectiveType || '未知' : '不支持'}</div>
              <div>用户代理: {typeof navigator !== 'undefined' ? navigator.userAgent.substring(0, 50) + '...' : '未知'}</div>
            </div>
          </details>
        </div>
      </div>
    </div>
  );
}

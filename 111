## 问题修复总结 - 2025年1月25日

### 已修复的问题：

1. **减少重复渲染和调试日志**
   - 将调试日志限制在开发环境中显示
   - 优化了Player组件的渲染逻辑
   - 减少了控制台输出的噪音

2. **改进图片加载错误处理**
   - 使用React状态管理失败的图片加载
   - 当图片加载失败时，立即显示📺备用图标
   - 避免了图片重复加载尝试

3. **优化用户体验**
   - 图片加载失败不再显示空白区域
   - 频道列表显示更加稳定
   - 减少了不必要的网络请求

### 修复前的问题：
- Player组件重复渲染导致多次日志输出
- 图片加载失败时显示空白区域
- 大量的图片加载错误请求

### 修复后的效果：
- 调试日志只在开发环境显示
- 图片加载失败时显示统一的📺图标
- 页面加载更加流畅，用户体验改善
Image
commitMount @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:19894
runWithFiberInDEV @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:872
commitHostMount @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12667
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13116
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13114
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13114
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13205
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13114
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13114
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13114
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13114
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13114
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13114
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13114
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13114
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13114
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13205
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13205
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13003
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13003
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13003
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13205
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13205
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13114
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13114
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13114
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13205
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13003
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13003
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13003
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13205
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13205
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13205
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13205
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13205
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13205
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13003
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13205
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:12998
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13205
recursivelyTraverseLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14071
commitLayoutEffectOnFiber @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:13080
flushLayoutEffects @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16106
commitRoot @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15947
commitRootWhenReady @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15178
performWorkOnRoot @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15097
performWorkOnRootViaSchedulerTask @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16766
performWorkUntilDeadline @ D:\Project\IPTV\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
<img>
exports.jsxDEV @ D:\Project\IPTV\node_modules\next\dist\compiled\react\cjs\react-jsx-dev-runtime.development.js:345
eval @ D:\Project\IPTV\src\components\ChannelList.tsx:269
eval @ D:\Project\IPTV\src\components\ChannelList.tsx:257
ChannelList @ D:\Project\IPTV\src\components\ChannelList.tsx:245
react_stack_bottom_frame @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23552
renderWithHooks @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:6763
updateFunctionComponent @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9069
beginWork @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:10679
runWithFiberInDEV @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:872
performUnitOfWork @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15677
workLoopSync @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15497
renderRootSync @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15477
performWorkOnRoot @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14941
performWorkOnRootViaSchedulerTask @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16766
performWorkUntilDeadline @ D:\Project\IPTV\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
<ChannelList>
exports.jsxDEV @ D:\Project\IPTV\node_modules\next\dist\compiled\react\cjs\react-jsx-dev-runtime.development.js:345
HomePage @ D:\Project\IPTV\src\app\page.tsx:299
react_stack_bottom_frame @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23552
renderWithHooks @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:6763
updateFunctionComponent @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9069
beginWork @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:10679
runWithFiberInDEV @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:872
performUnitOfWork @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15677
workLoopSync @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15497
renderRootSync @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15477
performWorkOnRoot @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14941
performWorkOnRootViaSchedulerTask @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16766
performWorkUntilDeadline @ D:\Project\IPTV\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
<HomePage>
exports.jsx @ D:\Project\IPTV\node_modules\next\dist\compiled\react\cjs\react-jsx-runtime.development.js:338
ClientPageRoot @ D:\src\client\components\client-page.tsx:60
react_stack_bottom_frame @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23552
renderWithHooks @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:6763
updateFunctionComponent @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9069
beginWork @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:10628
runWithFiberInDEV @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:872
performUnitOfWork @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15677
workLoopConcurrentByScheduler @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15671
renderRootConcurrent @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15646
performWorkOnRoot @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14940
performWorkOnRootViaSchedulerTask @ D:\Project\IPTV\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16766
performWorkUntilDeadline @ D:\Project\IPTV\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
"use client"
initializeElement @ D:\Project\IPTV\node_modules\next\dist\compiled\react-server-dom-webpack\cjs\react-server-dom-webpack-client.browser.development.js:1206
eval @ D:\Project\IPTV\node_modules\next\dist\compiled\react-server-dom-webpack\cjs\react-server-dom-webpack-client.browser.development.js:2830
initializeModelChunk @ D:\Project\IPTV\node_modules\next\dist\compiled\react-server-dom-webpack\cjs\react-server-dom-webpack-client.browser.development.js:1109
resolveModelChunk @ D:\Project\IPTV\node_modules\next\dist\compiled\react-server-dom-webpack\cjs\react-server-dom-webpack-client.browser.development.js:1084
resolveModel @ D:\Project\IPTV\node_modules\next\dist\compiled\react-server-dom-webpack\cjs\react-server-dom-webpack-client.browser.development.js:1907
processFullStringRow @ D:\Project\IPTV\node_modules\next\dist\compiled\react-server-dom-webpack\cjs\react-server-dom-webpack-client.browser.development.js:2664
processFullBinaryRow @ D:\Project\IPTV\node_modules\next\dist\compiled\react-server-dom-webpack\cjs\react-server-dom-webpack-client.browser.development.js:2606
processBinaryChunk @ D:\Project\IPTV\node_modules\next\dist\compiled\react-server-dom-webpack\cjs\react-server-dom-webpack-client.browser.development.js:2733
progress @ D:\Project\IPTV\node_modules\next\dist\compiled\react-server-dom-webpack\cjs\react-server-dom-webpack-client.browser.development.js:2997
"use server"
ResponseInstance @ D:\Project\IPTV\node_modules\next\dist\compiled\react-server-dom-webpack\cjs\react-server-dom-webpack-client.browser.development.js:1870
createResponseFromOptions @ D:\Project\IPTV\node_modules\next\dist\compiled\react-server-dom-webpack\cjs\react-server-dom-webpack-client.browser.development.js:2858
exports.createFromReadableStream @ D:\Project\IPTV\node_modules\next\dist\compiled\react-server-dom-webpack\cjs\react-server-dom-webpack-client.browser.development.js:3228
eval @ D:\src\client\app-index.tsx:157
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1754371496821:160
options.factory @ webpack.js:1
__webpack_require__ @ webpack.js:1
fn @ webpack.js:1
eval @ D:\src\client\app-next-dev.ts:17
eval @ D:\src\client\app-bootstrap.ts:78
loadScriptsInSequence @ D:\src\client\app-bootstrap.ts:20
appBootstrap @ D:\src\client\app-bootstrap.ts:60
eval @ D:\src\client\app-next-dev.ts:16
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1754371496821:182
options.factory @ webpack.js:1
__webpack_require__ @ webpack.js:1
__webpack_exec__ @ main-app.js?v=1754371496821:1790
(匿名) @ main-app.js?v=1754371496821:1791
webpackJsonpCallback @ webpack.js:1
(匿名) @ main-app.js?v=1754371496821:9
wapx.cmvideo.cn/publish/poms/image/5910/022/164/202501231715_1658138745288_H169_1080.jpg:1   GET https://wapx.cmvideo.cn/publish/poms/image/5910/022/164/202501231715_1658138745288_H169_1080.jpg net::ERR_CONNECTION_CLOSED
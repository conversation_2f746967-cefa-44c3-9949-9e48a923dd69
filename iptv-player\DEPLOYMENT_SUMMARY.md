# Windows 本地部署总结

## 🎯 快速部署指南

### 方式一：Docker 部署（推荐）

#### 前提条件
- 安装 Docker Desktop for Windows
- 确保 Docker 服务正在运行

#### 部署步骤
```bash
# 1. 进入项目目录
cd iptv-player

# 2. 使用启动脚本（推荐）
.\scripts\start.bat

# 或手动启动
docker-compose up -d --build

# 3. 访问应用
# 浏览器打开：http://localhost:9004
```

### 方式二：本地 Node.js 部署

#### 前提条件
- Node.js 18+ 
- npm 包管理器

#### 部署步骤
```bash
# 1. 安装依赖
npm install

# 2. 初始化数据库
npx prisma generate
npx prisma db push

# 3. 配置端口（可选）
# 创建 .env.local 文件：
echo PORT=9004 > .env.local
echo NEXTAUTH_URL=http://localhost:9004 >> .env.local

# 4. 启动应用
npm run dev  # 开发模式
# 或
npm run build && npm run start  # 生产模式

# 5. 访问应用
# 浏览器打开：http://localhost:9004 或 http://localhost:3000
```

## 🔧 端口配置

### 修改端口的最简方法

#### Docker 部署
只需修改 `docker-compose.yml` 中的一行：
```yaml
ports:
  - "8080:3000"  # 改为你想要的端口
environment:
  - NEXTAUTH_URL=http://localhost:8080  # 同步修改
```

#### 本地开发
创建 `.env.local` 文件：
```bash
PORT=8080
NEXTAUTH_URL=http://localhost:8080
```

## ✅ 验证部署成功

### 检查清单
- [ ] 应用能正常启动
- [ ] 浏览器能访问主页面
- [ ] 能够添加订阅源
- [ ] 能够播放视频（如果有有效的 M3U 源）

### 测试命令
```bash
# 检查应用是否运行
curl http://localhost:9004

# 检查 API 健康状态
curl http://localhost:9004/api/health

# 检查端口监听
netstat -ano | findstr :9004
```

## 🔧 常见问题解决

### 端口被占用
```bash
# 查看占用进程
netstat -ano | findstr :9004

# 结束进程（替换 PID）
taskkill /PID <PID> /F
```

### Docker 启动失败
```bash
# 查看日志
docker-compose logs

# 重新构建
docker-compose down
docker-compose up -d --build
```

### Node.js 权限问题
```bash
# 以管理员身份运行命令提示符
# 或使用 PowerShell
```

## 📁 重要文件说明

| 文件/目录 | 用途 | 是否可修改 |
|-----------|------|------------|
| `docker-compose.yml` | Docker 配置 | ✅ 端口映射 |
| `.env` | 通用环境变量 | ⚠️ 谨慎修改 |
| `.env.local` | 本地开发配置 | ✅ 可自由修改 |
| `data/` | 数据库文件 | ❌ 自动生成 |
| `scripts/` | 启动脚本 | ❌ 不建议修改 |

## 🚀 生产环境建议

### Docker 部署（推荐）
- 使用 Docker Compose 进行部署
- 配置数据卷持久化
- 设置健康检查
- 配置日志轮转

### 安全配置
- 修改默认的 `NEXTAUTH_SECRET`
- 配置防火墙规则
- 定期备份数据库文件
- 及时更新依赖包

### 性能优化
- 分配足够的内存给 Docker
- 使用 SSD 硬盘
- 配置适当的端口
- 监控系统资源使用

## 📚 详细文档

如需更详细的说明，请参考：
- [Windows 部署指南](./docs/WINDOWS_DEPLOYMENT.md)
- [端口配置指南](./docs/PORT_CONFIGURATION.md)
- [项目主文档](./README.md)

## 🎉 部署完成

恭喜！您已成功部署 IPTV 播放器。

### 下一步操作
1. 添加您的 M3U 订阅源
2. 浏览和播放频道
3. 根据需要调整设置
4. 享受观看体验！

---

**如有问题，请查看详细文档或提交 Issue。**

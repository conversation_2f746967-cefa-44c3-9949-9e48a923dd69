"use client"

import { useState } from 'react';
import { getTestChannels, getTestChannelGroups, getTestChannelsByGroup, convertToAppChannel, type TestChannel } from '@/lib/test-channels';

interface TestChannelsProps {
  onChannelSelect: (channel: any) => void;
  currentChannel?: any;
}

export default function TestChannels({ onChannelSelect, currentChannel }: TestChannelsProps) {
  const [selectedGroup, setSelectedGroup] = useState<string>('全部');
  const [isExpanded, setIsExpanded] = useState(false);

  const allChannels = getTestChannels();
  const groups = ['全部', ...getTestChannelGroups()];
  
  const filteredChannels = selectedGroup === '全部' 
    ? allChannels 
    : getTestChannelsByGroup(selectedGroup);

  const getTypeIcon = (type: TestChannel['type']) => {
    switch (type) {
      case 'hls': return '📺';
      case 'mp4': return '🎬';
      case 'webm': return '🎞️';
      case 'dash': return '⚡';
      default: return '📹';
    }
  };

  const getTypeColor = (type: TestChannel['type']) => {
    switch (type) {
      case 'hls': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'mp4': return 'bg-green-100 text-green-800 border-green-200';
      case 'webm': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'dash': return 'bg-orange-100 text-orange-800 border-orange-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      {/* 标题栏 */}
      <div 
        className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-3">
          <div className="text-2xl">🧪</div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              测试频道
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {filteredChannels.length} 个测试频道可用
            </p>
          </div>
        </div>
        <div className="text-gray-400">
          {isExpanded ? '▼' : '▶'}
        </div>
      </div>

      {/* 内容区域 */}
      {isExpanded && (
        <div className="p-4">
          {/* 分组筛选 */}
          <div className="mb-4">
            <div className="flex flex-wrap gap-2">
              {groups.map(group => (
                <button
                  key={group}
                  onClick={() => setSelectedGroup(group)}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    selectedGroup === group
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {group}
                </button>
              ))}
            </div>
          </div>

          {/* 频道列表 */}
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {filteredChannels.map(channel => {
              const isSelected = currentChannel?.id === channel.id;
              
              return (
                <div
                  key={channel.id}
                  onClick={() => onChannelSelect(convertToAppChannel(channel))}
                  className={`p-3 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                    isSelected
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-lg">{getTypeIcon(channel.type)}</span>
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {channel.name}
                        </h4>
                        <span className={`px-2 py-0.5 rounded-full text-xs border ${getTypeColor(channel.type)}`}>
                          {channel.type.toUpperCase()}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {channel.description}
                      </p>
                      
                      <div className="text-xs text-gray-500 dark:text-gray-500 font-mono bg-gray-100 dark:bg-gray-700 rounded px-2 py-1">
                        {channel.url.length > 60 ? `${channel.url.substring(0, 60)}...` : channel.url}
                      </div>
                    </div>
                    
                    {isSelected && (
                      <div className="ml-3 text-blue-600 dark:text-blue-400">
                        ✓
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* 使用说明 */}
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-start gap-2">
              <div className="text-blue-600 dark:text-blue-400 mt-0.5">💡</div>
              <div className="text-sm text-blue-800 dark:text-blue-200">
                <p className="font-medium mb-1">使用说明:</p>
                <ul className="space-y-1 text-xs">
                  <li>• <strong>HLS测试</strong>: 测试 HLS.js 播放器和自适应码率功能</li>
                  <li>• <strong>MP4/WebM测试</strong>: 测试原生视频播放器</li>
                  <li>• <strong>错误测试</strong>: 测试错误处理和恢复机制</li>
                  <li>• <strong>直播测试</strong>: 测试实时流媒体播放</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 统计信息 */}
          <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            {['hls', 'mp4', 'webm', 'dash'].map(type => {
              const count = allChannels.filter(ch => ch.type === type).length;
              return (
                <div key={type} className="p-2 bg-gray-50 dark:bg-gray-700 rounded">
                  <div className="text-lg">{getTypeIcon(type as TestChannel['type'])}</div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {type.toUpperCase()}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {count} 个
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET() {
  try {
    let settings = await prisma.settings.findUnique({
      where: { id: 'singleton' }
    })

    // 如果没有设置记录，创建默认设置
    if (!settings) {
      settings = await prisma.settings.create({
        data: {
          id: 'singleton',
          theme: 'dark',
          volume: 0.5,
          autoplay: false,
          quality: 'auto',
          language: 'zh-CN'
        }
      })
    }

    return NextResponse.json(settings)
  } catch (error) {
    console.error('Error fetching settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { theme, volume, autoplay, quality, language } = body

    // 使用upsert确保单例模式
    const settings = await prisma.settings.upsert({
      where: { id: 'singleton' },
      update: {
        ...(theme !== undefined && { theme }),
        ...(volume !== undefined && { volume }),
        ...(autoplay !== undefined && { autoplay }),
        ...(quality !== undefined && { quality }),
        ...(language !== undefined && { language })
      },
      create: {
        id: 'singleton',
        theme: theme || 'dark',
        volume: volume !== undefined ? volume : 0.5,
        autoplay: autoplay || false,
        quality: quality || 'auto',
        language: language || 'zh-CN'
      }
    })

    return NextResponse.json(settings)
  } catch (error) {
    console.error('Error updating settings:', error)
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    )
  }
}

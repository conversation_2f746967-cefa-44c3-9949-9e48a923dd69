'use client'

import React, { useState, useMemo } from 'react'
import { Search, Star, Play } from 'lucide-react'
import { Channel } from '@/types'
import { cn, formatDate } from '@/lib/utils'

interface ChannelListProps {
  channels: Channel[]
  onChannelSelect: (channel: Channel) => void
  selectedChannel?: Channel
  loading?: boolean
  error?: string
  className?: string
}

export default function ChannelList({
  channels,
  onChannelSelect,
  selectedChannel,
  loading = false,
  error,
  className
}: ChannelListProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedGroup, setSelectedGroup] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'list'>('list')
  const [sortBy, setSortBy] = useState<'name' | 'recent'>('name')
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false)
  const [favorites, setFavorites] = useState<Set<string>>(new Set())
  const [failedImages, setFailedImages] = useState<Set<string>>(new Set())

  // 从localStorage加载收藏
  React.useEffect(() => {
    const savedFavorites = localStorage.getItem('iptv-favorites')
    if (savedFavorites) {
      try {
        const favoriteIds = JSON.parse(savedFavorites)
        setFavorites(new Set(favoriteIds))
      } catch (error) {
        console.error('Failed to load favorites:', error)
      }
    }
  }, [])

  // 保存收藏到localStorage
  const saveFavorites = (newFavorites: Set<string>) => {
    localStorage.setItem('iptv-favorites', JSON.stringify(Array.from(newFavorites)))
  }

  // 切换收藏状态
  const toggleFavorite = (channelId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev)
      if (newFavorites.has(channelId)) {
        newFavorites.delete(channelId)
      } else {
        newFavorites.add(channelId)
      }
      saveFavorites(newFavorites)
      return newFavorites
    })
  }

  // 获取所有分组
  const groups = useMemo(() => {
    const groupSet = new Set(channels.map(channel => channel.group))
    return Array.from(groupSet).sort()
  }, [channels])

  // 过滤和排序频道
  const filteredChannels = useMemo(() => {
    let filtered = channels

    // 搜索过滤
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(channel =>
        channel.name.toLowerCase().includes(query) ||
        channel.group.toLowerCase().includes(query)
      )
    }

    // 分组过滤
    if (selectedGroup !== 'all') {
      filtered = filtered.filter(channel => channel.group === selectedGroup)
    }

    // 收藏过滤
    if (showFavoritesOnly) {
      filtered = filtered.filter(channel => favorites.has(channel.id))
    }

    // 排序（在分组内排序）
    filtered.sort((a, b) => {
      // 先按分组排序，再按选择的方式排序
      const groupCompare = a.group.localeCompare(b.group)
      if (groupCompare !== 0) return groupCompare

      // 在同一分组内排序
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'recent':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        default:
          return 0
      }
    })

    return filtered
  }, [channels, searchQuery, selectedGroup, sortBy, showFavoritesOnly, favorites])

  // 按分组组织频道
  const channelsByGroup = useMemo(() => {
    const grouped: Record<string, Channel[]> = {}
    
    filteredChannels.forEach(channel => {
      if (!grouped[channel.group]) {
        grouped[channel.group] = []
      }
      grouped[channel.group].push(channel)
    })
    
    return grouped
  }, [filteredChannels])

  if (loading) {
    return (
      <div className={cn('flex items-center justify-center h-64', className)}>
        <div className="flex flex-col items-center space-y-2">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
          <span className="text-sm text-gray-500">加载频道列表...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn('flex items-center justify-center h-64', className)}>
        <div className="text-center text-red-500">
          <div className="text-lg font-medium">加载失败</div>
          <div className="text-sm opacity-75">{error}</div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('flex flex-col h-full bg-background', className)}>
      {/* 搜索和过滤栏 */}
      <div className="p-4 border-b border-border bg-card/50 space-y-4">
        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="搜索频道..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2.5 border border-input rounded-lg bg-background text-foreground placeholder-muted-foreground focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 shadow-sm"
          />
        </div>

        {/* 过滤和视图控制 */}
        <div className="flex items-center justify-between gap-3">
          <div className="flex items-center gap-2">
            {/* 分组选择 */}
            <select
              value={selectedGroup}
              onChange={(e) => setSelectedGroup(e.target.value)}
              className="px-3 py-2 text-sm border border-input rounded-md bg-background text-foreground focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 shadow-sm min-w-[100px]"
            >
              <option value="all">所有分组</option>
              {groups.map(group => (
                <option key={group} value={group}>{group}</option>
              ))}
            </select>

            {/* 排序选择 */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 text-sm border border-input rounded-md bg-background text-foreground focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 shadow-sm min-w-[100px]"
            >
              <option value="name">按名称</option>
              <option value="recent">按更新时间</option>
            </select>
          </div>

          <div className="flex items-center gap-2">
            {/* 收藏过滤 */}
            <button
              onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
              className={cn(
                'p-2 rounded-md transition-all duration-200 shadow-sm',
                showFavoritesOnly
                  ? 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/50 dark:text-yellow-400 ring-1 ring-yellow-200 dark:ring-yellow-800'
                  : 'text-muted-foreground hover:text-foreground hover:bg-accent'
              )}
              title={showFavoritesOnly ? '显示所有频道' : '只显示收藏'}
            >
              <Star className={cn('w-4 h-4', showFavoritesOnly && 'fill-current')} />
            </button>


          </div>
        </div>

        {/* 统计信息 */}
        <div className="flex items-center justify-between text-xs text-muted-foreground bg-muted/30 px-3 py-2 rounded-md">
          <span>
            共 <span className="font-medium text-foreground">{filteredChannels.length}</span> 个频道
            {searchQuery && (
              <span className="ml-1">
                · 搜索 "<span className="font-medium text-foreground">{searchQuery}</span>"
              </span>
            )}
            {selectedGroup !== 'all' && (
              <span className="ml-1">
                · 分组 "<span className="font-medium text-foreground">{selectedGroup}</span>"
              </span>
            )}
          </span>
          {showFavoritesOnly && (
            <span className="text-yellow-600 dark:text-yellow-400 font-medium">
              仅收藏
            </span>
          )}
        </div>
      </div>

      {/* 频道列表 */}
      <div className="flex-1 overflow-y-auto">
        {filteredChannels.length === 0 ? (
          <div className="flex items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <div className="text-lg">没有找到频道</div>
              <div className="text-sm">请尝试调整搜索条件</div>
            </div>
          </div>
        ) : (
          // 列表视图
          <div className="divide-y divide-border/50">
            {Object.entries(channelsByGroup).map(([group, groupChannels]) => (
              <div key={group}>
                {selectedGroup === 'all' && (
                  <div className="sticky top-0 px-4 py-3 bg-muted/80 backdrop-blur-sm text-sm font-semibold text-foreground border-b border-border/50 z-10">
                    <div className="flex items-center justify-between">
                      <span>{group}</span>
                      <span className="text-xs text-muted-foreground bg-background px-2 py-1 rounded-full">
                        {groupChannels.length}
                      </span>
                    </div>
                  </div>
                )}
                {groupChannels.map(channel => (
                  <div
                    key={channel.id}
                    onClick={() => onChannelSelect(channel)}
                    className={cn(
                      'flex items-center p-4 hover:bg-accent/50 cursor-pointer transition-all duration-200 group',
                      selectedChannel?.id === channel.id && 'bg-primary/10 border-r-4 border-primary shadow-sm'
                    )}
                  >
                    {/* 频道图标 */}
                    <div className="flex-shrink-0 w-12 h-12 mr-4">
                      {channel.logo && !failedImages.has(channel.id) ? (
                        <img
                          src={channel.logo}
                          alt={channel.name}
                          className="w-full h-full object-cover rounded-lg shadow-sm ring-1 ring-border/20"
                          onError={() => {
                            setFailedImages(prev => new Set(prev).add(channel.id))
                          }}
                        />
                      ) : (
                        <div className="w-full h-full bg-muted rounded-lg flex items-center justify-center text-muted-foreground text-lg shadow-sm ring-1 ring-border/20">
                          📺
                        </div>
                      )}
                    </div>

                    {/* 频道信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="font-semibold text-foreground truncate group-hover:text-primary transition-colors">
                        {channel.name}
                      </div>
                      <div className="text-sm text-muted-foreground truncate mt-1">
                        {channel.group}
                      </div>
                    </div>

                    {/* 播放指示器和收藏按钮 */}
                    <div className="flex items-center gap-2 flex-shrink-0">
                      {/* 收藏按钮 */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          toggleFavorite(channel.id)
                        }}
                        className={cn(
                          'p-1 rounded-md transition-all duration-200 opacity-0 group-hover:opacity-100',
                          favorites.has(channel.id)
                            ? 'text-yellow-500 bg-yellow-50 dark:bg-yellow-900/20 opacity-100'
                            : 'text-muted-foreground hover:text-yellow-500 hover:bg-yellow-50 dark:hover:bg-yellow-900/20'
                        )}
                        title={favorites.has(channel.id) ? '取消收藏' : '收藏频道'}
                      >
                        <Star className={cn('w-4 h-4', favorites.has(channel.id) && 'fill-current')} />
                      </button>

                      {/* 播放指示器 */}
                      {selectedChannel?.id === channel.id && (
                        <div className="flex items-center gap-1 text-primary">
                          <Play className="w-4 h-4 fill-current" />
                          <span className="text-xs font-medium">播放中</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

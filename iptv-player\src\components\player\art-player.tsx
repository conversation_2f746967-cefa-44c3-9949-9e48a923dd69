"use client"

import { useEffect, useRef, useState } from 'react'
import Artplayer from 'artplayer'
import Hls from 'hls.js'

interface ArtPlayerProps {
  url: string
  poster?: string
  title?: string
  isLive?: boolean
  useProxy?: boolean
  onReady?: (art: Artplayer) => void
  onError?: (error: Error | unknown) => void
}

export default function ArtPlayerComponent({
  url,
  poster,
  title,
  isLive = true,
  useProxy = true,
  onReady,
  onError
}: ArtPlayerProps) {
  const artRef = useRef<HTMLDivElement>(null)
  const playerRef = useRef<Artplayer | null>(null)
  const currentUrlRef = useRef<string>('')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    console.log('ArtPlayer useEffect triggered:', { url, hasContainer: !!artRef.current })

    if (!artRef.current || !url) {
      console.log('Missing container or URL:', { container: !!artRef.current, url })
      return
    }

    console.log('ArtPlayer received URL:', url)

    // 根据useProxy决定是否使用代理URL
    const finalUrl = useProxy ? url : (url.includes('/api/proxy?url=') ? decodeURIComponent(url.split('url=')[1]) : url)
    console.log('Final URL for player:', finalUrl, 'useProxy:', useProxy)
    console.log('Creating new player with URL:', finalUrl)

    // 如果URL没有变化，不重新创建播放器
    if (playerRef.current && currentUrlRef.current === finalUrl) {
      console.log('URL unchanged, skipping recreation')
      return
    }

    // 清理之前的播放器实例
    if (playerRef.current) {
      console.log('Destroying previous player instance')
      playerRef.current.destroy()
      playerRef.current = null
    }

    setIsLoading(true)
    setError(null)

    try {
      // 启用调试模式
      if (typeof window !== 'undefined') {
        (window as any).Artplayer = Artplayer
        Artplayer.DEBUG = true
      }

      console.log('Initializing ArtPlayer with config:', {
        url: finalUrl,
        poster,
        isLive,
        container: artRef.current
      })

      const art = new Artplayer({
        container: artRef.current,
        url: finalUrl,
        poster: poster || '',
        volume: 0.5,
        isLive: isLive,
        muted: false,
        autoplay: false,
        pip: true,
        setting: true,
        playbackRate: !isLive,
        aspectRatio: true,
        fullscreen: true,
        fullscreenWeb: true,
        subtitleOffset: false,
        miniProgressBar: true,
        mutex: true,
        backdrop: true,
        playsInline: true,
        autoSize: false,
        autoMini: false,
        screenshot: true,
        airplay: true,
        theme: '#00d4ff',
        lang: 'zh-cn',
        moreVideoAttr: {
          crossOrigin: 'anonymous',
        },
        customType: {
          m3u8: function (video: HTMLVideoElement, url: string, art: Artplayer) {
            console.log('Loading M3U8 stream:', url)

            // 首先尝试原生支持（仅在Safari等浏览器中）
            if (video.canPlayType('application/vnd.apple.mpegurl') && /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)) {
              console.log('Using native HLS support (Safari)')
              video.src = url
              return {
                destroy: () => {},
              }
            }

            // 如果不支持原生HLS，使用HLS.js
            if (Hls.isSupported()) {
              console.log('Using HLS.js')
              const hls = new Hls({
                debug: true, // 启用调试以获取更多信息
                enableWorker: false, // 禁用Web Worker以提高兼容性
                lowLatencyMode: false, // 禁用低延迟模式，提高兼容性

                // 缓冲区设置 - 更保守的设置
                backBufferLength: 30, // 减少后缓冲区
                maxBufferLength: 20, // 减少最大缓冲区
                maxMaxBufferLength: 300, // 减少最大最大缓冲区

                // 超时和重试设置
                manifestLoadingTimeOut: 20000,
                manifestLoadingMaxRetry: 3,
                manifestLoadingRetryDelay: 500,
                levelLoadingTimeOut: 20000,
                levelLoadingMaxRetry: 3,
                levelLoadingRetryDelay: 500,
                fragLoadingTimeOut: 20000,
                fragLoadingMaxRetry: 3,
                fragLoadingRetryDelay: 500,

                // 错误恢复设置
                appendErrorMaxRetry: 3,

                // 质量和播放设置
                capLevelToPlayerSize: false,
                startLevel: -1,
                autoStartLoad: true,

                // 容错设置
                maxFragLookUpTolerance: 0.2, // 减少容错性，更严格
                liveSyncDurationCount: 3,
                liveMaxLatencyDurationCount: 10,
                maxLiveSyncPlaybackRate: 1.5,

                // 高级兼容性设置
                forceKeyFrameOnDiscontinuity: true, // 强制关键帧
                abrEwmaFastLive: 3.0, // 调整自适应比特率
                abrEwmaSlowLive: 9.0,
                abrEwmaFastVoD: 3.0,
                abrEwmaSlowVoD: 9.0,
                abrEwmaDefaultEstimate: 500000, // 默认带宽估计
                abrBandWidthFactor: 0.95, // 带宽因子
                abrBandWidthUpFactor: 0.7, // 上行带宽因子
                maxStarvationDelay: 4, // 最大饥饿延迟
                maxLoadingDelay: 4, // 最大加载延迟

                // XHR设置
                xhrSetup: function(xhr: XMLHttpRequest, url: string) {
                  xhr.withCredentials = false
                  xhr.timeout = 20000
                  // 添加更多头部以提高兼容性
                  xhr.setRequestHeader('Accept', '*/*')
                  xhr.setRequestHeader('Accept-Language', 'en-US,en;q=0.9')
                }
              })

              // 监听各种事件以便调试
              hls.on(Hls.Events.MANIFEST_LOADED, () => {
                console.log('HLS: Manifest loaded successfully')
              })

              hls.on(Hls.Events.LEVEL_LOADED, (event, data) => {
                console.log('HLS: Level loaded:', data.level)
              })

              hls.on(Hls.Events.FRAG_LOADED, (event, data) => {
                console.log('HLS: Fragment loaded:', data.frag.url)
              })

              hls.on(Hls.Events.ERROR, (event, data) => {
                console.error('HLS Error:', {
                  type: data.type,
                  details: data.details,
                  fatal: data.fatal,
                  url: data.url,
                  response: data.response,
                  error: data.error,
                  reason: data.reason
                })

                // 特殊处理DEMUXER错误 - 尝试使用简化的HLS.js配置
                if (data.details && (data.details.includes('DEMUXER') || data.details === 'demuxerError')) {
                  console.log('Demuxer error detected, trying simplified HLS.js configuration...')

                  // 销毁当前HLS.js实例
                  hls.destroy()

                  // 创建一个简化的HLS.js实例
                  try {
                    console.log('Creating simplified HLS.js instance')
                    const simpleHls = new Hls({
                      debug: false,
                      enableWorker: false,
                      lowLatencyMode: false,
                      // 最简单的配置
                      maxBufferLength: 10,
                      maxMaxBufferLength: 60,
                      manifestLoadingTimeOut: 10000,
                      manifestLoadingMaxRetry: 2,
                      levelLoadingTimeOut: 10000,
                      levelLoadingMaxRetry: 2,
                      fragLoadingTimeOut: 10000,
                      fragLoadingMaxRetry: 2,
                      appendErrorMaxRetry: 1,
                      capLevelToPlayerSize: false,
                      startLevel: -1,
                      autoStartLoad: true,
                    })

                    simpleHls.loadSource(url)
                    simpleHls.attachMedia(video)

                    simpleHls.on(Hls.Events.ERROR, (event, errorData) => {
                      console.error('Simplified HLS error:', errorData)
                      // 如果简化版本也失败，尝试原生播放器
                      if (errorData.fatal) {
                        console.log('Simplified HLS also failed, trying native player')
                        simpleHls.destroy()
                        video.src = url
                        video.load()
                      }
                    })

                  } catch (e) {
                    console.error('Simplified HLS failed, trying native player:', e)
                    video.src = url
                    video.load()
                  }
                  return
                }

                // 显示用户友好的错误信息
                let errorMessage = '播放出现问题'

                if (data.details === Hls.ErrorDetails.MANIFEST_LOAD_ERROR) {
                  errorMessage = '无法加载视频源，请检查网络连接'
                } else if (data.details === Hls.ErrorDetails.FRAG_LOAD_ERROR) {
                  errorMessage = '视频片段加载失败，可能是网络问题'
                } else if (data.details === Hls.ErrorDetails.KEY_LOAD_ERROR) {
                  errorMessage = '视频解密密钥加载失败'
                } else if (data.details === Hls.ErrorDetails.LEVEL_LOAD_ERROR) {
                  errorMessage = '视频质量信息加载失败'
                } else if (data.details && data.details.includes('DEMUXER')) {
                  errorMessage = '视频格式解析失败，正在尝试其他方式'
                }

                if (data.fatal) {
                  switch (data.type) {
                    case Hls.ErrorTypes.NETWORK_ERROR:
                      console.log('Fatal network error, trying to recover...')
                      art.notice.show = errorMessage + '，正在重试...'
                      setTimeout(() => {
                        hls.startLoad()
                      }, 1000)
                      break
                    case Hls.ErrorTypes.MEDIA_ERROR:
                      console.log('Fatal media error, trying to recover...')
                      art.notice.show = '媒体解码错误，正在恢复...'
                      hls.recoverMediaError()
                      break
                    default:
                      console.log('Fatal error, cannot recover:', data.details)
                      art.notice.show = errorMessage + '，无法恢复'
                      hls.destroy()
                      break
                  }
                } else {
                  // 非致命错误，显示警告但继续播放
                  art.notice.show = errorMessage + '，但播放将继续'
                }
              })

              hls.loadSource(url)
              hls.attachMedia(video)

              // 存储hls实例以便清理
              ;(video as HTMLVideoElement & { hls?: Hls }).hls = hls
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
              console.log('Using native HLS support')
              video.src = url
            } else {
              console.error('HLS is not supported in this browser')
              art.notice.show = '当前浏览器不支持HLS播放'
            }
          },
        },
        icons: {
          loading: '<div class="art-loading-icon"></div>',
          state: '<div class="art-state-icon"></div>',
        },
        controls: [
          {
            position: 'right',
            html: '画质',
            tooltip: '画质选择',
            click: function () {
              console.log('Quality selector clicked')
            },
          },
        ],
        contextmenu: [
          {
            html: '复制视频地址',
            click: function () {
              navigator.clipboard.writeText(url)
              art.notice.show = '视频地址已复制'
            },
          },
        ],
      })

      // 事件监听
      art.on('ready', () => {
        console.log('ArtPlayer ready')
        setIsLoading(false)
        onReady?.(art)
      })

      art.on('error', (error, reconnectTime) => {
        console.error('Player error:', { error, reconnectTime })
        console.error('Video element error:', art.video?.error)

        let errorMessage = '播放器加载失败'

        // 检查video元素的错误
        if (art.video?.error) {
          const videoError = art.video.error
          console.error('Video error details:', {
            code: videoError.code,
            message: videoError.message,
            MEDIA_ERR_ABORTED: MediaError.MEDIA_ERR_ABORTED,
            MEDIA_ERR_NETWORK: MediaError.MEDIA_ERR_NETWORK,
            MEDIA_ERR_DECODE: MediaError.MEDIA_ERR_DECODE,
            MEDIA_ERR_SRC_NOT_SUPPORTED: MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED
          })

          switch (videoError.code) {
            case MediaError.MEDIA_ERR_ABORTED:
              errorMessage = '视频播放被中止'
              break
            case MediaError.MEDIA_ERR_NETWORK:
              errorMessage = '网络错误，无法加载视频'
              break
            case MediaError.MEDIA_ERR_DECODE:
              errorMessage = '视频解码错误'
              break
            case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
              errorMessage = '视频格式不支持或源无法访问'
              break
            default:
              errorMessage = `视频错误 (代码: ${videoError.code})`
          }
        }

        setError(errorMessage)
        setIsLoading(false)
        onError?.(error)
      })

      art.on('video:error', (event) => {
        console.error('Video error event:', event)
        const videoElement = art.video as HTMLVideoElement
        let errorMessage = '视频加载失败'

        if (videoElement && videoElement.error) {
          const videoError = videoElement.error
          console.error('Video error details:', {
            code: videoError.code,
            message: videoError.message,
            src: videoElement.src,
            currentSrc: videoElement.currentSrc,
            networkState: videoElement.networkState,
            readyState: videoElement.readyState
          })

          // 特殊处理DEMUXER错误 - 尝试原生播放器
          if (videoError.message && videoError.message.includes('DEMUXER_ERROR_DETECTED_HLS')) {
            console.log('DEMUXER_ERROR_DETECTED_HLS found, trying native player...')

            // 尝试使用原生播放器
            setTimeout(() => {
              console.log('Switching to native video player...')
              if (art && art.video) {
                // 直接设置原始URL，绕过HLS.js
                const originalUrl = videoElement.src
                console.log('Setting native video source:', originalUrl)
                art.video.src = originalUrl
                art.video.load()
              }
            }, 1000)

            errorMessage = '正在尝试原生播放器...'
          } else {
            switch (videoError.code) {
            case MediaError.MEDIA_ERR_ABORTED:
              errorMessage = '视频播放被中止'
              break
            case MediaError.MEDIA_ERR_NETWORK:
              errorMessage = '网络错误，请检查网络连接或视频源'
              break
            case MediaError.MEDIA_ERR_DECODE:
              errorMessage = '视频解码错误，可能是格式不支持'
              break
            case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
              errorMessage = '视频源不支持或无法访问，请检查URL是否正确'
              break
            default:
              errorMessage = `未知的视频错误 (代码: ${videoError.code})`
            }
          }
        } else {
          console.error('No video error object available')
          errorMessage = '视频加载失败，无法获取详细错误信息'
        }

        setError(errorMessage)
        setIsLoading(false)
        onError?.(event)
      })

      art.on('video:canplay', () => {
        console.log('Video can play')
        setIsLoading(false)
        setError(null)
      })

      art.on('video:loadstart', () => {
        console.log('Video load start')
        setIsLoading(true)
        setError(null)
      })

      art.on('video:loadeddata', () => {
        console.log('Video loaded data')
        setIsLoading(false)
      })

      art.on('video:waiting', () => {
        console.log('Video waiting for data')
      })

      art.on('video:playing', () => {
        console.log('Video playing')
        setError(null)
      })

      art.on('video:stalled', () => {
        console.warn('Video stalled')
      })

      playerRef.current = art
      currentUrlRef.current = finalUrl

    } catch (error) {
      console.error('Failed to initialize player:', error)
      setError('播放器初始化失败')
      setIsLoading(false)
      onError?.(error)
    }

    return () => {
      if (playerRef.current) {
        // 清理HLS实例
        const video = playerRef.current.video as HTMLVideoElement & { hls?: Hls }
        if (video.hls) {
          video.hls.destroy()
        }
        
        playerRef.current.destroy()
        playerRef.current = null
      }
    }
  }, [url]) // 只依赖URL变化

  return (
    <div className="relative w-full h-full bg-black rounded-lg overflow-hidden">
      <div ref={artRef} className="w-full h-full" />
      
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="flex flex-col items-center space-y-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            <span className="text-white text-sm">加载中...</span>
          </div>
        </div>
      )}
      
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75">
          <div className="text-center text-white p-4 max-w-md">
            <div className="text-red-400 mb-2 text-2xl">⚠️</div>
            <div className="text-sm mb-4">{error}</div>
            <div className="space-y-2">
              <button
                onClick={() => {
                  setError(null)
                  setIsLoading(true)
                  // 重新初始化播放器
                  if (playerRef.current) {
                    playerRef.current.destroy()
                    playerRef.current = null
                  }
                  currentUrlRef.current = ''
                }}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                重试播放
              </button>
              <button
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
              >
                刷新页面
              </button>
            </div>
            <div className="mt-4 text-xs text-gray-400">
              <p>如果问题持续存在，请检查：</p>
              <ul className="text-left mt-1 space-y-1">
                <li>• 网络连接是否正常</li>
                <li>• 视频源是否有效</li>
                <li>• 浏览器是否支持该格式</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

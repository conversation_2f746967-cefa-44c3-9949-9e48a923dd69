# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/

# Production
build

# Misc
.DS_Store
*.tsbuildinfo

# Debug
*.log

# Local env files
.env*.local
.env.development
.env.test

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode
.idea

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
docs/

# Tests
__tests__
*.test.*
*.spec.*

# Development files
data/
config/

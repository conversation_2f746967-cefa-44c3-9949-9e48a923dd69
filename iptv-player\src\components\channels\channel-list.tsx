"use client"

import { useState, useMemo } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card, CardContent } from '@/components/ui/card'
import { Search, Heart, Play, Tv } from 'lucide-react'
import { cn } from '@/lib/utils'

interface Channel {
  id: string
  name: string
  url: string
  logo?: string | null
  group?: string | null
  isFavorite: boolean
}

interface ChannelListProps {
  channels: Channel[]
  currentChannelId?: string
  onChannelSelect: (channel: Channel) => void
  onToggleFavorite: (channelId: string) => void
  className?: string
}

export default function ChannelList({
  channels,
  currentChannelId,
  onChannelSelect,
  onToggleFavorite,
  className
}: ChannelListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null)
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false)

  // 获取所有分组
  const groups = useMemo(() => {
    const groupSet = new Set<string>()
    channels.forEach(channel => {
      if (channel.group) {
        groupSet.add(channel.group)
      }
    })
    return Array.from(groupSet).sort()
  }, [channels])

  // 过滤频道
  const filteredChannels = useMemo(() => {
    return channels.filter(channel => {
      // 搜索过滤
      if (searchTerm && !channel.name.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false
      }
      
      // 分组过滤
      if (selectedGroup && channel.group !== selectedGroup) {
        return false
      }
      
      // 收藏过滤
      if (showFavoritesOnly && !channel.isFavorite) {
        return false
      }
      
      return true
    })
  }, [channels, searchTerm, selectedGroup, showFavoritesOnly])

  const handleChannelClick = (channel: Channel) => {
    onChannelSelect(channel)
  }

  const handleFavoriteClick = (e: React.MouseEvent, channelId: string) => {
    e.stopPropagation()
    onToggleFavorite(channelId)
  }

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* 搜索和过滤 */}
      <div className="p-4 border-b space-y-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="搜索频道..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="space-y-2">
          <div className="flex flex-wrap gap-2">
            <Button
              variant={showFavoritesOnly ? "default" : "outline"}
              size="sm"
              onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
              className="h-8"
            >
              <Heart className="h-3 w-3 mr-1" />
              收藏
            </Button>

            <Button
              variant={selectedGroup === null ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedGroup(null)}
              className="h-8"
            >
              全部
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            {groups.map(group => (
              <Button
                key={group}
                variant={selectedGroup === group ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedGroup(group)}
                className="h-8 text-xs"
              >
                {group}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* 频道列表 */}
      <div className="flex-1 overflow-y-auto">
        {filteredChannels.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
            <Tv className="h-12 w-12 mb-2" />
            <p>没有找到频道</p>
            {searchTerm && (
              <Button
                variant="link"
                onClick={() => setSearchTerm('')}
                className="mt-2"
              >
                清除搜索
              </Button>
            )}
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {filteredChannels.map((channel) => (
              <Card
                key={channel.id}
                className={cn(
                  "cursor-pointer transition-all hover:bg-accent",
                  currentChannelId === channel.id && "bg-accent border-primary"
                )}
                onClick={() => handleChannelClick(channel)}
              >
                <CardContent className="p-3">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={channel.logo || undefined} alt={channel.name} />
                      <AvatarFallback>
                        <Tv className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium truncate">{channel.name}</h3>
                        <div className="flex items-center space-x-1">
                          {currentChannelId === channel.id && (
                            <Play className="h-3 w-3 text-primary" />
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => handleFavoriteClick(e, channel.id)}
                            className="h-6 w-6 p-0"
                          >
                            <Heart
                              className={cn(
                                "h-3 w-3",
                                channel.isFavorite ? "fill-red-500 text-red-500" : "text-muted-foreground"
                              )}
                            />
                          </Button>
                        </div>
                      </div>
                      
                      {channel.group && (
                        <Badge variant="secondary" className="text-xs mt-1">
                          {channel.group}
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* 统计信息 */}
      <div className="p-4 border-t text-sm text-muted-foreground">
        显示 {filteredChannels.length} / {channels.length} 个频道
      </div>
    </div>
  )
}

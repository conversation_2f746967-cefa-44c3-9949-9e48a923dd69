// 测试频道数据 - 用于演示和测试不同格式的视频流

export interface TestChannel {
  id: string;
  name: string;
  url: string;
  type: 'hls' | 'mp4' | 'webm' | 'dash';
  description: string;
  logo?: string;
  group: string;
}

export const testChannels: TestChannel[] = [
  // HLS 测试流 - 使用更可靠的源
  {
    id: 'test-hls-1',
    name: 'Apple HLS 测试流 (多码率)',
    url: 'https://devstreaming-cdn.apple.com/videos/streaming/examples/img_bipbop_adv_example_fmp4/master.m3u8',
    type: 'hls',
    description: 'Apple 官方 HLS 测试流，支持多码率自适应',
    group: 'HLS测试'
  },
  {
    id: 'test-hls-2',
    name: 'Apple HLS 基础测试流',
    url: 'https://devstreaming-cdn.apple.com/videos/streaming/examples/bipbop_4x3/bipbop_4x3_variant.m3u8',
    type: 'hls',
    description: 'Apple 官方基础 HLS 测试流',
    group: 'HLS测试'
  },
  {
    id: 'test-hls-3',
    name: 'HLS.js 官方测试流',
    url: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
    type: 'hls',
    description: 'Mux 提供的 HLS 测试流',
    group: 'HLS测试'
  },
  {
    id: 'test-hls-4',
    name: 'Big Buck Bunny HLS',
    url: 'https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8',
    type: 'hls',
    description: 'Unified Streaming 提供的测试流',
    group: 'HLS测试'
  },

  // MP4 测试文件 - 使用更可靠的源
  {
    id: 'test-mp4-1',
    name: 'Big Buck Bunny (MP4) - 官方源',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    type: 'mp4',
    description: '经典测试视频 - Google官方测试源',
    group: 'MP4测试'
  },
  {
    id: 'test-mp4-2',
    name: 'Elephant Dream (MP4)',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    type: 'mp4',
    description: '开源电影 - Elephant Dream',
    group: 'MP4测试'
  },
  {
    id: 'test-mp4-3',
    name: 'For Bigger Blazes (MP4)',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    type: 'mp4',
    description: '4K测试视频 - Google官方源',
    group: 'MP4测试'
  },
  {
    id: 'test-mp4-4',
    name: 'Sintel (MP4)',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4',
    type: 'mp4',
    description: '开源电影 - Sintel',
    group: 'MP4测试'
  },

  // WebM 测试文件
  {
    id: 'test-webm-1',
    name: 'Sample Video (WebM)',
    url: 'https://sample-videos.com/zip/10/webm/SampleVideo_1280x720_1mb.webm',
    type: 'webm',
    description: 'WebM 格式测试视频',
    group: 'WebM测试'
  },

  // 直播测试流
  {
    id: 'test-live-1',
    name: 'NASA Live Stream',
    url: 'https://nasa-i.akamaihd.net/hls/live/253565/NASA-NTV1-HLS/master.m3u8',
    type: 'hls',
    description: 'NASA 官方直播流',
    group: '直播测试'
  },
  {
    id: 'test-live-2',
    name: 'Weather Channel',
    url: 'https://weather-lh.akamaihd.net/i/twc_1@92006/master.m3u8',
    type: 'hls',
    description: '天气频道直播流',
    group: '直播测试'
  },

  // 错误测试（用于测试错误处理）
  {
    id: 'test-error-1',
    name: '404错误测试',
    url: 'https://example.com/nonexistent.m3u8',
    type: 'hls',
    description: '用于测试404错误处理',
    group: '错误测试'
  },
  {
    id: 'test-error-2',
    name: 'CORS错误测试',
    url: 'https://httpbin.org/status/403',
    type: 'hls',
    description: '用于测试CORS和权限错误',
    group: '错误测试'
  }
];

// 获取测试频道的函数
export function getTestChannels(): TestChannel[] {
  return testChannels;
}

// 根据类型获取测试频道
export function getTestChannelsByType(type: TestChannel['type']): TestChannel[] {
  return testChannels.filter(channel => channel.type === type);
}

// 根据分组获取测试频道
export function getTestChannelsByGroup(group: string): TestChannel[] {
  return testChannels.filter(channel => channel.group === group);
}

// 获取所有分组
export function getTestChannelGroups(): string[] {
  return [...new Set(testChannels.map(channel => channel.group))];
}

// 转换为应用程序使用的频道格式
export function convertToAppChannel(testChannel: TestChannel) {
  return {
    id: testChannel.id,
    name: testChannel.name,
    url: testChannel.url,
    group: testChannel.group,
    logo: testChannel.logo || undefined
  };
}

// 获取推荐的测试频道（稳定可用的）
export function getRecommendedTestChannels(): TestChannel[] {
  return testChannels.filter(channel => 
    !channel.group.includes('错误测试') && 
    (channel.type === 'mp4' || channel.id === 'test-hls-1')
  );
}

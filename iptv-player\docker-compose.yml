version: '3.8'

services:
  iptv-player:
    build: .
    container_name: iptv-player
    ports:
      - "9004:3000"
    volumes:
      # 数据持久化
      - ./data:/app/data
      # 配置文件（可选）
      - ./config:/app/config
    environment:
      # 数据库配置
      - DATABASE_URL=file:/app/data/iptv.db
      # Next.js配置
      - NODE_ENV=production
      - NEXTAUTH_SECRET=your-secret-key-change-this-in-production
      - NEXTAUTH_URL=http://localhost:9004
      # 应用配置
      - PORT=3000
      - HOSTNAME=0.0.0.0
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - iptv-network

networks:
  iptv-network:
    driver: bridge

volumes:
  iptv-data:
    driver: local

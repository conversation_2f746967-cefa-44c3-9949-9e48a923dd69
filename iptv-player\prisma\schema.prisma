// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Subscription {
  id          String    @id @default(cuid())
  name        String
  url         String    @unique // 防止重复订阅源
  type        String    // M3U, M3U8
  isActive    Boolean   @default(true)
  lastUpdated DateTime  @default(now())
  channels    Channel[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([isActive]) // 优化活跃订阅源查询
  @@index([lastUpdated]) // 优化按更新时间排序
}

model Channel {
  id             String       @id @default(cuid())
  name           String
  url            String
  logo           String?
  group          String?
  subscriptionId String
  subscription   Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)
  isFavorite     Boolean      @default(false)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@index([subscriptionId]) // 优化按订阅源查询
  @@index([isFavorite]) // 优化收藏频道查询
  @@index([group]) // 优化按分组查询
  @@index([name]) // 优化按名称搜索
  @@unique([url, subscriptionId]) // 防止同一订阅源内重复频道
}

model Settings {
  id          String  @id @default("singleton") // 单例模式，固定ID
  theme       String  @default("dark")
  volume      Float   @default(0.5)
  autoplay    Boolean @default(false)
  quality     String  @default("auto")
  language    String  @default("zh-CN")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

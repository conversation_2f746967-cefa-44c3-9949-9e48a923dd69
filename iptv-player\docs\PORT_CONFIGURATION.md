# 端口配置指南

## 📋 默认端口配置

### Docker 部署
- **外部端口**: 9004
- **内部端口**: 3000
- **访问地址**: http://localhost:9004

### 本地开发
- **默认端口**: 3000
- **访问地址**: http://localhost:3000

## 🔧 修改端口的方法

### Docker 部署修改端口

#### 方法：修改 docker-compose.yml

**步骤 1**: 编辑 `docker-compose.yml` 文件
```yaml
services:
  app:
    ports:
      - "8080:3000"  # 将外部端口改为 8080
    environment:
      - NEXTAUTH_URL=http://localhost:8080  # 同步修改认证URL
```

**步骤 2**: 重启容器
```bash
docker-compose down
docker-compose up -d
```

**结果**: 应用将在 http://localhost:8080 运行

### 本地开发修改端口

#### 方法一：环境变量（临时）

**Windows 命令提示符**:
```bash
set PORT=8080 && npm run dev
```

**Windows PowerShell**:
```bash
$env:PORT=8080; npm run dev
```

**Linux/macOS**:
```bash
PORT=8080 npm run dev
```

#### 方法二：.env.local 文件（推荐）

**步骤 1**: 创建 `.env.local` 文件
```bash
# 本地开发端口配置
PORT=8080
NEXTAUTH_URL=http://localhost:8080
```

**步骤 2**: 启动应用
```bash
npm run dev
```

**结果**: 应用将在 http://localhost:8080 运行

#### 方法三：修改 package.json（永久）

**步骤 1**: 编辑 `package.json`
```json
{
  "scripts": {
    "dev": "next dev --turbopack -p 8080",
    "start": "next start -p 8080"
  }
}
```

**步骤 2**: 更新 NEXTAUTH_URL
在 `.env` 文件中：
```bash
NEXTAUTH_URL=http://localhost:8080
```

**步骤 3**: 启动应用
```bash
npm run dev
```

## 🎯 最佳实践

### 推荐的配置方式

#### 开发环境
使用 `.env.local` 文件：
```bash
# .env.local（不提交到 Git）
PORT=9004
NEXTAUTH_URL=http://localhost:9004
```

#### 生产环境
使用 Docker 部署，在 `docker-compose.yml` 中配置：
```yaml
services:
  app:
    ports:
      - "9004:3000"
    environment:
      - NEXTAUTH_URL=http://localhost:9004
```

### 环境变量优先级

1. **系统环境变量** - 最高优先级
2. **`.env.local`** - 本地开发配置
3. **`.env`** - 通用配置
4. **默认值** - 最低优先级

### 文件说明

| 文件 | 用途 | 是否提交到 Git |
|------|------|----------------|
| `.env` | 通用配置 | ✅ 是 |
| `.env.local` | 本地开发配置 | ❌ 否 |
| `.env.example` | 配置示例 | ✅ 是 |

## 🔍 端口冲突解决

### 检查端口占用

**Windows**:
```bash
netstat -ano | findstr :9004
```

**Linux/macOS**:
```bash
lsof -i :9004
```

### 结束占用进程

**Windows**:
```bash
# 查看进程 PID
netstat -ano | findstr :9004

# 结束进程（替换 <PID> 为实际进程ID）
taskkill /PID <PID> /F
```

**Linux/macOS**:
```bash
# 结束进程
kill -9 $(lsof -t -i:9004)
```

### 选择其他端口

常用的替代端口：
- 8080 - 常用的 Web 开发端口
- 8000 - Python Django 默认端口
- 8888 - Jupyter Notebook 默认端口
- 3001 - React 开发服务器备用端口
- 5000 - Flask 默认端口

## ⚠️ 注意事项

### NEXTAUTH_URL 必须同步修改

当修改端口时，**必须**同时修改 `NEXTAUTH_URL` 环境变量：

```bash
# 错误示例 - 端口不匹配
PORT=8080
NEXTAUTH_URL=http://localhost:9004  # ❌ 错误

# 正确示例 - 端口匹配
PORT=8080
NEXTAUTH_URL=http://localhost:8080  # ✅ 正确
```

### Docker 内外端口区别

Docker 部署时要区分内外端口：

```yaml
ports:
  - "9004:3000"  # 外部端口:内部端口
```

- **外部端口 9004**: 浏览器访问的端口
- **内部端口 3000**: 容器内应用监听的端口
- **健康检查**: 使用内部端口 3000

### 防火墙配置

修改端口后，可能需要配置防火墙：

**Windows 防火墙**:
1. 打开"Windows 安全中心"
2. 选择"防火墙和网络保护"
3. 点击"允许应用通过防火墙"
4. 添加端口或应用程序

## 🧪 测试端口配置

### 验证端口是否正常工作

1. **启动应用**
2. **检查端口监听**:
```bash
# Windows
netstat -an | findstr :9004

# Linux/macOS
netstat -an | grep :9004
```

3. **浏览器访问**: http://localhost:9004
4. **检查健康状态**: http://localhost:9004/api/health

### 常见测试命令

```bash
# 测试端口连通性
telnet localhost 9004

# 使用 curl 测试
curl http://localhost:9004

# 测试 API 端点
curl http://localhost:9004/api/health
```

## 📝 配置示例

### 完整的 .env.local 示例
```bash
# 端口配置
PORT=9004

# 认证配置
NEXTAUTH_URL=http://localhost:9004
NEXTAUTH_SECRET=your-secret-key-here

# 数据库配置
DATABASE_URL=file:./dev.db

# 环境配置
NODE_ENV=development
```

### 完整的 docker-compose.yml 端口配置
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "9004:3000"
    environment:
      - DATABASE_URL=file:/app/data/dev.db
      - NEXTAUTH_SECRET=your-secret-key-here
      - NEXTAUTH_URL=http://localhost:9004
      - NODE_ENV=production
    volumes:
      - ./data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

## 🔗 相关文档

- [Windows 部署指南](./WINDOWS_DEPLOYMENT.md)
- [Docker 配置说明](../docker-compose.yml)
- [环境变量配置](./.env.example)

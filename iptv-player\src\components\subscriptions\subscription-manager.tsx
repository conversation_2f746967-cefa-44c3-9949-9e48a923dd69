"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Trash2, Plus, RefreshCw, Download, Calendar } from 'lucide-react'
import { cn } from '@/lib/utils'

interface Subscription {
  id: string
  name: string
  url: string
  type: string
  isActive: boolean
  lastUpdated: Date
  channelCount?: number
}

interface SubscriptionManagerProps {
  subscriptions: Subscription[]
  onAdd: (subscription: Omit<Subscription, 'id' | 'lastUpdated'>) => Promise<void>
  onDelete: (id: string) => Promise<void>
  onUpdate: (id: string) => Promise<void>
  onToggleActive: (id: string, isActive: boolean) => Promise<void>
  className?: string
}

export default function SubscriptionManager({
  subscriptions,
  onAdd,
  onDelete,
  onUpdate,
  onToggleActive,
  className
}: SubscriptionManagerProps) {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newSubscription, setNewSubscription] = useState({
    name: '',
    url: '',
    type: 'M3U'
  })
  const [isLoading, setIsLoading] = useState<string | null>(null)

  const handleAdd = async () => {
    if (!newSubscription.name || !newSubscription.url) return

    try {
      setIsLoading('add')
      await onAdd({
        ...newSubscription,
        isActive: true,
        channelCount: 0
      })
      setNewSubscription({ name: '', url: '', type: 'M3U' })
      setIsAddDialogOpen(false)
    } catch (error) {
      console.error('Failed to add subscription:', error)
    } finally {
      setIsLoading(null)
    }
  }

  const handleUpdate = async (id: string) => {
    try {
      setIsLoading(id)
      await onUpdate(id)
    } catch (error) {
      console.error('Failed to update subscription:', error)
    } finally {
      setIsLoading(null)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除这个订阅源吗？这将同时删除所有相关频道。')) return

    try {
      setIsLoading(id)
      await onDelete(id)
    } catch (error) {
      console.error('Failed to delete subscription:', error)
    } finally {
      setIsLoading(null)
    }
  }

  const handleToggleActive = async (id: string, isActive: boolean) => {
    try {
      await onToggleActive(id, isActive)
    } catch (error) {
      console.error('Failed to toggle subscription:', error)
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">订阅源管理</h2>
        <div className="flex items-center gap-3">
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="px-6 py-2.5 min-w-[140px]">
                <Plus className="h-4 w-4 mr-2" />
                添加订阅源
              </Button>
            </DialogTrigger>
            <DialogContent>
            <DialogHeader>
              <DialogTitle>添加新订阅源</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">名称</Label>
                <Input
                  id="name"
                  placeholder="订阅源名称"
                  value={newSubscription.name}
                  onChange={(e) => setNewSubscription(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="url">URL</Label>
                <Input
                  id="url"
                  placeholder="https://example.com/playlist.m3u"
                  value={newSubscription.url}
                  onChange={(e) => setNewSubscription(prev => ({ ...prev, url: e.target.value }))}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  取消
                </Button>
                <Button 
                  onClick={handleAdd}
                  disabled={!newSubscription.name || !newSubscription.url || isLoading === 'add'}
                >
                  {isLoading === 'add' ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      添加中...
                    </>
                  ) : (
                    '添加'
                  )}
                </Button>
              </div>
            </div>
          </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 订阅源列表 */}
      <div className="grid gap-4">
        {subscriptions.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Download className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">暂无订阅源</h3>
              <p className="text-muted-foreground text-center mb-4">
                添加M3U订阅源来开始观看IPTV频道
              </p>
              <Button onClick={() => setIsAddDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                添加第一个订阅源
              </Button>
            </CardContent>
          </Card>
        ) : (
          subscriptions.map((subscription) => (
            <Card key={subscription.id} className={cn(!subscription.isActive && "opacity-60")}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CardTitle className="text-lg">{subscription.name}</CardTitle>
                    <Badge variant={subscription.isActive ? "default" : "secondary"}>
                      {subscription.isActive ? "启用" : "禁用"}
                    </Badge>
                    <Badge variant="outline">{subscription.type}</Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUpdate(subscription.id)}
                      disabled={isLoading === subscription.id}
                    >
                      {isLoading === subscription.id ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleToggleActive(subscription.id, !subscription.isActive)}
                    >
                      {subscription.isActive ? "禁用" : "启用"}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(subscription.id)}
                      disabled={isLoading === subscription.id}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <span className="font-medium mr-2">URL:</span>
                    <span className="truncate">{subscription.url}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      <span>更新时间: {formatDate(subscription.lastUpdated)}</span>
                    </div>
                    {subscription.channelCount !== undefined && (
                      <span>{subscription.channelCount} 个频道</span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}

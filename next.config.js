/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用standalone输出模式，用于Docker部署
  output: 'standalone',

  // 禁用React严格模式以避免开发环境中的重复渲染
  reactStrictMode: false,

  // 外部包配置 - 使用新的配置选项
  serverExternalPackages: ['sqlite3'],

  // 简化的webpack配置
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // 只处理必要的Node.js模块fallback
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        sqlite3: false
      }
    }

    // 处理媒体资源
    config.module.rules.push({
      test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)$/,
      type: 'asset/resource'
    })

    return config
  },

  // 图片配置
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: '**',
      }
    ],
    unoptimized: process.env.NODE_ENV === 'development'
  },

  // 生产优化 - 移除已废弃的swcMinify
  compress: true,

  // 环境变量
  env: {
    NEXT_PUBLIC_APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || 'IPTV Player',
    NEXT_PUBLIC_APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0'
  },

  // 异步重写规则（用于代理）
  async rewrites() {
    return [
      // 代理规则可以在这里添加，但我们使用 API 路由
    ]
  },

  // 头部配置
  async headers() {
    return [
      {
        source: '/api/proxy',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ]
  }
}

module.exports = nextConfig

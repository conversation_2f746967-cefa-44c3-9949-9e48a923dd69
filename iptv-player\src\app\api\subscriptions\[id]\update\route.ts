import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { fetchM3U, parseM3U } from '@/lib/m3u-parser'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // 获取订阅源信息
    const subscription = await prisma.subscription.findUnique({
      where: { id }
    })

    if (!subscription) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 }
      )
    }

    // 获取并解析M3U内容
    const m3uContent = await fetchM3U(subscription.url)
    const parsed = await parseM3U(m3uContent)

    // 删除现有频道
    await prisma.channel.deleteMany({
      where: { subscriptionId: id }
    })

    // 创建新频道
    if (parsed.channels.length > 0) {
      await prisma.channel.createMany({
        data: parsed.channels.map(channel => ({
          name: channel.name,
          url: channel.url,
          logo: channel.logo,
          group: channel.group,
          subscriptionId: id
        }))
      })
    }

    // 更新订阅源的最后更新时间
    const updatedSubscription = await prisma.subscription.update({
      where: { id },
      data: { lastUpdated: new Date() }
    })

    return NextResponse.json({
      subscription: updatedSubscription,
      channelsCount: parsed.channels.length
    })
  } catch (error) {
    console.error('Error updating subscription:', error)
    return NextResponse.json(
      { error: 'Failed to update subscription' },
      { status: 500 }
    )
  }
}

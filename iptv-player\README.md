# IPTV Player - 现代化网页直播播放器

一个基于 Next.js 和 HLS.js 构建的现代化 IPTV 网页播放器，支持 M3U/M3U8 订阅源管理，具有响应式设计和深色主题支持。

## ✨ 特性

### 🎯 核心功能
- **多格式支持**: 智能支持 HLS (m3u8)、MP4、WebM 等视频格式，自动选择最佳播放方式
- **订阅源管理**: 支持添加和管理多个 M3U/M3U8 订阅源，防重复检测
- **频道管理**: 频道列表显示、搜索、分类和收藏功能，支持分页
- **直播播放**: 基于 HLS.js 的高性能视频播放器，支持自适应码率和错误恢复
- **实时更新**: 支持订阅源自动更新和手动刷新

### 🎨 界面设计
- **响应式设计**: 完美适配桌面、平板和移动设备
- **现代化 UI**: 基于 shadcn/ui 的美观界面设计
- **主题切换**: 支持深色/浅色主题切换
- **错误处理**: 友好的错误提示和自动重试机制

### 🚀 技术特性
- **高性能**: Next.js 15 + React 18 + TypeScript，优化的数据库索引
- **数据持久化**: SQLite 数据库存储，事务保护
- **容器化部署**: Docker Compose 一键部署
- **安全性**: 输入验证、XSS 防护、超时控制等安全措施
- **稳定性**: 网络重试、内存保护、错误恢复机制

### 🎬 播放器特性
- **智能格式检测**: 自动识别 HLS、MP4、WebM 等格式，选择最佳播放方式
- **自适应码率**: HLS 流自动调整画质，适应网络状况
- **错误恢复**: 网络中断自动重连，媒体错误智能恢复
- **跨浏览器兼容**: 支持 Chrome、Firefox、Safari、Edge 等主流浏览器
- **实时调试**: 详细的播放状态和错误信息，便于问题排查

## 🛠️ 技术栈

- **前端框架**: Next.js 15 (App Router)
- **UI 组件**: shadcn/ui + Tailwind CSS
- **视频播放器**: HLS.js (智能降级到原生播放器)
- **数据库**: SQLite + Prisma ORM
- **主题管理**: next-themes
- **开发语言**: TypeScript
- **容器化**: Docker + Docker Compose

## 📦 快速开始

### 方式一：Docker 部署（推荐用于生产环境）

#### Windows 用户
1. **安装 Docker Desktop**
   - 下载：https://www.docker.com/products/docker-desktop/
   - 安装并启动 Docker Desktop

2. **使用启动脚本**
```bash
# 双击运行或在命令行执行
.\scripts\start.bat
```

3. **手动启动（可选）**
```bash
docker-compose up -d --build
```

4. **访问应用**
打开浏览器访问 `http://localhost:9004`

#### Linux/macOS 用户
```bash
# 使用启动脚本
./scripts/start.sh

# 或手动启动
docker-compose up -d --build
```

### 方式二：Windows 本地部署

#### 开发模式（推荐用于开发调试）
1. **环境准备**
```bash
# 确保已安装 Node.js 18+
node --version  # 应显示 v18.0.0 或更高版本
npm --version
```

2. **安装依赖**
```bash
cd iptv-player
npm install
```

3. **数据库初始化**
```bash
npx prisma generate
npx prisma db push
```

4. **配置端口（可选）**
创建 `.env.local` 文件：
```bash
# 本地开发端口配置
PORT=9004
NEXTAUTH_URL=http://localhost:9004
```

5. **启动开发服务器**
```bash
npm run dev
```

6. **访问应用**
- 如果配置了端口：`http://localhost:9004`
- 默认端口：`http://localhost:3000`

#### 生产模式（推荐用于本地生产环境）
1. **构建应用**
```bash
npm run build
```

2. **启动生产服务**
```bash
# 使用环境变量指定端口
PORT=9004 npm run start

# 或使用默认端口 3000
npm run start
```

3. **访问应用**
打开浏览器访问对应端口

## 🔧 配置说明

### 端口配置

#### 默认端口
- **Docker 部署**: 外部端口 `9004`，内部端口 `3000`
- **本地开发**: 默认端口 `3000`，可通过环境变量修改

#### 修改端口的方法

**Docker 部署修改端口：**
只需修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "8080:3000"  # 修改为 8080 端口
environment:
  - NEXTAUTH_URL=http://localhost:8080  # 同步修改认证URL
```

**本地开发修改端口：**
方法一：使用环境变量
```bash
PORT=8080 npm run dev
```

方法二：创建 `.env.local` 文件
```bash
PORT=8080
NEXTAUTH_URL=http://localhost:8080
```

### 环境变量

| 变量名 | 说明 | 默认值 | 必需 |
|--------|------|--------|------|
| `DATABASE_URL` | 数据库连接字符串 | `file:./dev.db` | ✅ |
| `NEXTAUTH_SECRET` | 认证密钥（生产环境必须修改） | - | ✅ |
| `NEXTAUTH_URL` | 应用完整URL（包含端口） | `http://localhost:3000` | ✅ |
| `NODE_ENV` | 运行环境 | `development` | ❌ |
| `PORT` | 应用端口 | `3000` | ❌ |

### 环境变量文件优先级

1. `.env.local` - 本地开发配置（不提交到Git）
2. `.env` - 通用配置
3. 系统环境变量

### Docker 配置

#### 端口映射
- `9004:3000` - Web 服务端口（外部端口9004映射到容器内部端口3000）

#### 数据卷
- `./data:/app/data` - 数据库文件持久化
- `./config:/app/config` - 配置文件（可选）

#### 健康检查
- 检查路径：`/api/health`
- 检查间隔：30秒
- 超时时间：10秒

## 📖 使用指南

### 1. 添加订阅源

1. 点击"订阅管理"标签页
2. 点击"添加订阅源"按钮
3. 输入订阅源名称和 M3U/M3U8 URL
4. 点击"添加"完成

### 2. 观看直播

1. 在"播放器"或"频道列表"标签页中选择频道
2. 播放器将自动加载并开始播放
3. 使用播放器控制栏进行音量、全屏等操作

### 3. 管理频道

- **搜索频道**: 在频道列表顶部的搜索框中输入关键词
- **分类筛选**: 点击分组标签筛选特定分类的频道
- **收藏频道**: 点击频道旁的心形图标添加到收藏
- **查看收藏**: 点击"收藏"按钮只显示收藏的频道

### 4. 更新订阅源

1. 在"订阅管理"页面找到要更新的订阅源
2. 点击刷新按钮手动更新
3. 系统会重新获取 M3U 文件并更新频道列表

## � 故障排除

### 常见问题

#### 1. 端口冲突
**问题**: 启动时提示端口被占用
**解决方案**:
```bash
# 查看端口占用
netstat -ano | findstr :9004

# 修改为其他端口
PORT=8080 npm run dev
```

#### 2. Docker 启动失败
**问题**: Docker 容器无法启动
**解决方案**:
```bash
# 查看日志
docker-compose logs

# 重新构建
docker-compose down
docker-compose up -d --build
```

#### 3. 数据库连接失败
**问题**: 数据库初始化失败
**解决方案**:
```bash
# 重新生成数据库
npx prisma generate
npx prisma db push

# 如果仍有问题，删除数据库文件重新创建
rm dev.db
npx prisma db push
```

#### 4. M3U 订阅源无法加载
**问题**: 添加订阅源后频道列表为空
**解决方案**:
- 检查 M3U URL 是否可访问
- 确认 M3U 文件格式正确
- 查看浏览器控制台错误信息
- 尝试手动更新订阅源

#### 5. 视频无法播放
**问题**: 频道列表正常但视频无法播放
**解决方案**:
- 检查视频流 URL 是否有效
- 确认网络连接正常
- 尝试其他频道
- 检查浏览器是否支持视频格式

### 日志查看

#### Docker 部署
```bash
# 查看应用日志
docker-compose logs app

# 实时查看日志
docker-compose logs -f app
```

#### 本地开发
- 查看终端输出
- 检查浏览器开发者工具控制台

### 性能优化

#### 大量频道处理
- 使用分页功能避免一次加载过多频道
- 定期清理无效的订阅源
- 使用搜索和分类功能快速定位频道

#### 内存使用优化
- 避免同时打开过多标签页
- 定期重启应用释放内存
- 监控系统资源使用情况

## �🔌 API 接口

### 频道管理
- `GET /api/channels` - 获取频道列表
- `POST /api/channels` - 添加频道
- `PUT /api/channels` - 更新频道（收藏状态）
- `DELETE /api/channels` - 删除频道

### 订阅源管理
- `GET /api/subscriptions` - 获取订阅源列表
- `POST /api/subscriptions` - 添加订阅源
- `PUT /api/subscriptions` - 更新订阅源状态
- `DELETE /api/subscriptions` - 删除订阅源
- `POST /api/subscriptions/[id]/update` - 更新订阅源内容

### 系统设置
- `GET /api/settings` - 获取系统设置
- `PUT /api/settings` - 更新系统设置
- `GET /api/health` - 健康检查

## 📁 项目结构

```
iptv-player/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API 路由
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局
│   │   └── page.tsx           # 主页面
│   ├── components/            # React 组件
│   │   ├── ui/               # shadcn/ui 组件
│   │   ├── player/           # 播放器组件
│   │   ├── channels/         # 频道相关组件
│   │   └── subscriptions/    # 订阅源组件
│   └── lib/                  # 工具函数
│       ├── db.ts            # 数据库连接
│       ├── m3u-parser.ts    # M3U 解析器
│       └── utils.ts         # 通用工具
├── prisma/                   # 数据库模式
├── public/                   # 静态资源
├── docker-compose.yml        # Docker Compose 配置
├── Dockerfile               # Docker 镜像配置
└── README.md               # 项目文档
```

## 🚀 部署指南

### NAS 设备部署

1. **准备环境**
   - 确保 NAS 支持 Docker 和 Docker Compose
   - 创建项目目录

2. **启动服务**
```bash
docker-compose up -d
```

3. **配置反向代理**（可选）
   - 如果使用 Nginx 等反向代理，配置相应规则

### 生产环境部署

1. **修改环境变量**
   - 更改 `NEXTAUTH_SECRET` 为安全的随机字符串
   - 设置正确的 `NEXTAUTH_URL`

2. **配置 HTTPS**（推荐）
   - 使用 Nginx 或 Traefik 配置 SSL 证书

3. **数据备份**
   - 定期备份 `./data` 目录中的数据库文件

## 🛡️ 安全注意事项

1. **更改默认密钥**: 生产环境中务必更改 `NEXTAUTH_SECRET`
2. **网络安全**: 建议在内网环境中使用，或配置适当的防火墙规则
3. **数据备份**: 定期备份数据库文件
4. **更新维护**: 及时更新依赖包和系统补丁

## 📚 详细文档

### 部署指南
- [Windows 部署指南](./docs/WINDOWS_DEPLOYMENT.md) - Windows 系统详细部署说明
- [端口配置指南](./docs/PORT_CONFIGURATION.md) - 端口修改和配置详解

### 更新日志
- [CHANGELOG.md](./CHANGELOG.md) - 版本更新记录和改进说明

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

### 开发环境设置
1. Fork 本仓库
2. 克隆到本地：`git clone <your-fork-url>`
3. 安装依赖：`npm install`
4. 启动开发服务器：`npm run dev`

### 提交规范
- 使用清晰的提交信息
- 遵循现有的代码风格
- 添加必要的测试
- 更新相关文档

## � 许可证

本项目采用 MIT 许可证。

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React 框架
- [HLS.js](https://github.com/video-dev/hls.js/) - HLS 视频播放器
- [shadcn/ui](https://ui.shadcn.com/) - UI 组件库
- [Prisma](https://prisma.io/) - 数据库 ORM
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架

---

**享受您的 IPTV 观看体验！** 🎉

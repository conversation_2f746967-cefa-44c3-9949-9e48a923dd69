import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const url = searchParams.get('url')

  if (!url) {
    return NextResponse.json({ error: 'URL parameter is required' }, { status: 400 })
  }

  try {
    console.log('Proxying request to:', url)

    // 发起请求到目标URL，自动跟随重定向
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Referer': 'https://www.example.com/',
        'Connection': 'keep-alive',
      },
      // 自动跟随重定向
      redirect: 'follow'
    })

    console.log('Final response status:', response.status)
    console.log('Final response URL:', response.url)
    console.log('Response headers:', Object.fromEntries(response.headers.entries()))

    // 检查响应状态
    if (!response.ok) {
      console.error('Response not ok:', response.status, response.statusText)

      // 对于某些常见的错误，提供更友好的错误信息
      let errorMessage = 'Failed to fetch resource'
      if (response.status === 403) {
        errorMessage = 'Access forbidden - stream may have geographic restrictions'
      } else if (response.status === 404) {
        errorMessage = 'Stream not found - URL may be invalid or expired'
      } else if (response.status === 500) {
        errorMessage = 'Server error - stream source may be temporarily unavailable'
      }

      return NextResponse.json(
        {
          error: errorMessage,
          status: response.status,
          statusText: response.statusText,
          url: url
        },
        {
          status: response.status,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          }
        }
      )
    }

    // 获取内容类型
    let contentType = response.headers.get('content-type') || 'application/octet-stream'

    // 检查是否为M3U8文件（只检查URL中是否包含.m3u8且不包含ts参数）
    const isM3U8 = (url.includes('.m3u8') || response.url.includes('.m3u8')) &&
                   !url.includes('ts=') && !response.url.includes('ts=')

    let body: ArrayBuffer | string
    let finalContentType = contentType

    if (isM3U8) {
      // 对于M3U8文件，读取为文本并处理URL
      const text = await response.text()
      console.log('Original M3U8 content preview:', text.substring(0, 300))

      // 获取基础URL（去掉文件名）
      const baseUrl = url.substring(0, url.lastIndexOf('/') + 1)
      console.log('Base URL for M3U8:', baseUrl)

      // 处理M3U8文件中的URL
      let processedText = text

      // 1. 先处理绝对URL
      processedText = processedText.replace(
        /https?:\/\/[^\s\n]+/g,
        (match) => {
          // 编码原始URL
          const encodedUrl = encodeURIComponent(match)
          // 返回代理URL
          console.log(`Converting absolute URL: ${match} -> /api/proxy?url=${encodedUrl}`)
          return `/api/proxy?url=${encodedUrl}`
        }
      )

      // 2. 处理相对URL（不以http开头，不以#开头，不为空行）
      processedText = processedText.replace(
        /^([^#\n\r\s][^\n\r]*\.(?:m3u8|ts))$/gm,
        (match, relativePath) => {
          // 构建完整的URL
          const fullUrl = baseUrl + relativePath.trim()
          // 转换为代理URL
          const encodedUrl = encodeURIComponent(fullUrl)
          const proxyUrl = `/api/proxy?url=${encodedUrl}`
          console.log(`Converting relative URL: ${relativePath} -> ${proxyUrl}`)
          return proxyUrl
        }
      )

      body = processedText
      finalContentType = 'application/vnd.apple.mpegurl'
      console.log('Processed M3U8 file, converted URLs to proxy format')
      console.log('Processed M3U8 content preview:', processedText.substring(0, 300))
    } else {
      // 对于其他文件，直接传输
      body = await response.arrayBuffer()
    }

    // 创建响应
    const proxyResponse = new NextResponse(body, {
      status: response.status,
      statusText: response.statusText,
    })

    // 设置CORS头部
    proxyResponse.headers.set('Access-Control-Allow-Origin', '*')
    proxyResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    proxyResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')

    // 设置内容类型
    proxyResponse.headers.set('Content-Type', finalContentType)

    // 如果是M3U8文件，设置额外的头部
    if (isM3U8) {
      proxyResponse.headers.set('Cache-Control', 'no-cache')
      // 删除可能导致下载的头部
      proxyResponse.headers.delete('Content-Disposition')
      console.log('Set M3U8 content type for:', response.url)
    }

    return proxyResponse

  } catch (error) {
    console.error('Proxy error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to proxy request', 
        details: error instanceof Error ? error.message : 'Unknown error',
        url: url
      }, 
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      }
    )
  }
}

// 处理OPTIONS请求（预检请求）
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}

'use client'

import React, { useEffect, useRef, useState } from 'react'
import { Channel, PlayerConfig, AppError, UserSettings } from '@/types'
import { cn, storage } from '@/lib/utils'

// 全局实例跟踪器
const playerInstances = new WeakMap<HTMLElement, any>()

// URL代理处理函数
function getProxiedUrl(originalUrl: string): string {
  if (!originalUrl) return ''

  // 检查是否需要使用代理
  if (originalUrl.startsWith('http://') || originalUrl.startsWith('https://')) {
    try {
      const url = new URL(originalUrl)
      const isLocalhost = url.hostname === 'localhost' ||
                         url.hostname === '127.0.0.1' ||
                         url.hostname === '0.0.0.0'

      if (!isLocalhost) {
        // 对所有非 localhost 的地址使用代理（包括局域网地址）
        return `/api/proxy?url=${encodeURIComponent(originalUrl)}`
      }
    } catch (error) {
      console.warn('URL parsing error:', error)
    }
  }

  return originalUrl
}

interface PlayerProps {
  channel?: Channel
  config?: Partial<PlayerConfig>
  onError?: (error: AppError) => void
  onReady?: () => void
  className?: string
}

export default function Player({ 
  channel, 
  config = {}, 
  onError, 
  onReady,
  className 
}: PlayerProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const playerRef = useRef<any>(null)
  const hlsRef = useRef<any>(null)
  const initializingRef = useRef(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isPlayerReady, setIsPlayerReady] = useState(false)

  // 从设置中读取用户配置
  const userSettings = storage.get('userSettings', {
    autoplay: false,
    volume: 0.7,
    startMuted: true
  } as Partial<UserSettings>)

  // 默认播放器配置
  const defaultConfig: PlayerConfig = {
    isLive: true,
    autoplay: userSettings.autoplay || false,
    muted: userSettings.startMuted !== false,
    volume: userSettings.volume || 0.7,
    controls: true,
    fullscreen: true,
    pip: true,
    screenshot: true,
    hotkey: true,
    miniProgressBar: true,
    setting: true,
    flip: true,
    playbackRate: true,
    aspectRatio: true,
    ...config
  }

  // 初始化播放器
  useEffect(() => {
    let isMounted = true

    const initPlayer = async () => {
      if (!containerRef.current || initializingRef.current || !channel) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Skipping player init:', {
            hasContainer: !!containerRef.current,
            isInitializing: initializingRef.current,
            hasChannel: !!channel
          })
        }
        return
      }

      initializingRef.current = true

      // 强制清理容器中的所有内容
      const container = containerRef.current
      if (container) {
        // 检查是否已有实例
        const existingInstance = playerInstances.get(container)
        if (existingInstance) {
          try {
            existingInstance.destroy()
          } catch (error) {
            console.warn('Error destroying existing instance:', error)
          }
          playerInstances.delete(container)
        }

        // 清理所有子元素
        while (container.firstChild) {
          container.removeChild(container.firstChild)
        }

        // 移除所有事件监听器
        const newContainer = container.cloneNode(false) as HTMLDivElement
        container.parentNode?.replaceChild(newContainer, container)
        containerRef.current = newContainer
      }

      // 清理之前的实例
      if (hlsRef.current) {
        try {
          hlsRef.current.destroy()
        } catch (error) {
          console.warn('Error destroying previous HLS instance:', error)
        }
        hlsRef.current = null
      }
      if (playerRef.current) {
        try {
          playerRef.current.destroy()
        } catch (error) {
          console.warn('Error destroying previous player instance:', error)
        }
        playerRef.current = null
      }

      // 检查组件是否仍然挂载
      if (!isMounted || !containerRef.current) {
        initializingRef.current = false
        return
      }

      // 检查容器尺寸
      const containerRect = containerRef.current.getBoundingClientRect()
      console.log('Container dimensions:', {
        width: containerRect.width,
        height: containerRect.height,
        offsetWidth: containerRef.current.offsetWidth,
        offsetHeight: containerRef.current.offsetHeight
      })

      if (containerRect.width === 0 || containerRect.height === 0) {
        console.warn('Container has zero dimensions, retrying...')
        initializingRef.current = false
        setTimeout(() => {
          if (isMounted) initPlayer()
        }, 100)
        return
      }

      try {
        // 动态导入ArtPlayer和HLS.js，添加更好的错误处理
        const [artplayerModule, hlsModule] = await Promise.all([
          import('artplayer').catch(err => {
            console.error('Failed to load ArtPlayer:', err)
            throw new Error('播放器组件加载失败')
          }),
          import('hls.js').catch(err => {
            console.error('Failed to load HLS.js:', err)
            throw new Error('HLS支持组件加载失败')
          })
        ])

        const Artplayer = artplayerModule.default
        const Hls = hlsModule.default

        // 检查ArtPlayer是否正确加载
        if (!Artplayer) {
          throw new Error('ArtPlayer未正确加载')
        }

        // 再次检查组件是否仍然挂载
        if (!isMounted || !containerRef.current) {
          initializingRef.current = false
          return
        }

        // 添加延迟以确保DOM完全清理
        await new Promise(resolve => setTimeout(resolve, 50))

        // 最终检查
        if (!isMounted || !containerRef.current) {
          initializingRef.current = false
          return
        }

        // 只有在有频道时才初始化播放器
        if (!channel) {
          console.log('No channel selected, skipping player initialization')
          initializingRef.current = false
          return
        }

        const videoUrl = getProxiedUrl(channel.url)
        console.log('Initializing player with URL:', videoUrl)

        // 创建播放器实例
        console.log('Creating ArtPlayer with:', {
          container: !!containerRef.current,
          containerSize: containerRef.current ? {
            width: containerRef.current.offsetWidth,
            height: containerRef.current.offsetHeight
          } : null,
          channel: channel?.name,
          url: videoUrl
        })

        const player = new Artplayer({
          container: containerRef.current,
          url: videoUrl,
          // 不设置poster，避免显示频道logo
          title: channel?.name || '请选择频道',
          volume: defaultConfig.volume,
          isLive: defaultConfig.isLive,
          muted: defaultConfig.muted,
          autoplay: defaultConfig.autoplay,
          pip: defaultConfig.pip,
          airplay: true,
          setting: defaultConfig.setting,
          hotkey: defaultConfig.hotkey,
          flip: defaultConfig.flip,
          playbackRate: defaultConfig.playbackRate,
          aspectRatio: defaultConfig.aspectRatio,
          fullscreen: defaultConfig.fullscreen,
          fullscreenWeb: true,
          subtitleOffset: true,
          miniProgressBar: defaultConfig.miniProgressBar,
          mutex: true,
          backdrop: true,
          playsInline: true,
          autoSize: false,
          screenshot: defaultConfig.screenshot,
          theme: '#00a1d6',
          lang: 'zh-cn',
          moreVideoAttr: {
            crossOrigin: 'anonymous',
            preload: 'none',  // 优化：不预加载，减少初始加载时间
            playsInline: true,
            muted: false,
          },
          // 自定义类型处理器
          customType: {
            m3u8: (video: HTMLVideoElement, url: string) => {
              if (Hls.isSupported()) {
                if (hlsRef.current) {
                  hlsRef.current.destroy()
                }
                
                const hls = new Hls({
                  debug: process.env.NODE_ENV === 'development',
                  enableWorker: true,
                  lowLatencyMode: true,
                  // 优化缓冲区设置，提升播放启动速度
                  backBufferLength: 30,        // 减少后缓冲区
                  maxBufferLength: 15,         // 减少最大缓冲区
                  maxBufferSize: 30 * 1000 * 1000, // 减少到30MB
                  maxBufferHole: 0.5,          // 增加缓冲区空洞容忍度，提高容错性
                  highBufferWatchdogPeriod: 2, // 减少监控频率，避免过于敏感
                  nudgeOffset: 0.1,            // 增加nudge偏移，提高容错性
                  nudgeMaxRetry: 5,            // 增加重试次数
                  maxMaxBufferLength: 300,     // 减少最大缓冲区
                  maxLoadingDelay: 4,          // 增加加载延迟容忍度
                  minAutoBitrate: 0,
                  emeEnabled: true,
                  widevineLicenseUrl: undefined,
                  // 启动优化
                  startFragPrefetch: true,     // 启用片段预取
                  testBandwidth: false,        // 禁用带宽测试
                  // 错误恢复配置
                  fragLoadPolicy: {
                    default: {
                      maxTimeToFirstByteMs: 10000,
                      maxLoadTimeMs: 120000,
                      timeoutRetry: {
                        maxNumRetry: 4,
                        retryDelayMs: 0,
                        maxRetryDelayMs: 0
                      },
                      errorRetry: {
                        maxNumRetry: 6,
                        retryDelayMs: 1000,
                        maxRetryDelayMs: 8000
                      }
                    }
                  },
                  manifestLoadPolicy: {
                    default: {
                      maxTimeToFirstByteMs: 10000,
                      maxLoadTimeMs: 20000,
                      timeoutRetry: {
                        maxNumRetry: 2,
                        retryDelayMs: 0,
                        maxRetryDelayMs: 0
                      },
                      errorRetry: {
                        maxNumRetry: 3,
                        retryDelayMs: 1000,
                        maxRetryDelayMs: 8000
                      }
                    }
                  },
                  // 自定义加载器，确保所有请求都通过代理
                  loader: class extends Hls.DefaultConfig.loader {
                    load(context: any, config: any, callbacks: any) {
                      const { url } = context

                      // 检查是否需要代理
                      if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
                        try {
                          const urlObj = new URL(url)
                          const isLocalhost = urlObj.hostname === 'localhost' ||
                                             urlObj.hostname === '127.0.0.1' ||
                                             urlObj.hostname === '0.0.0.0'

                          if (!isLocalhost && !url.includes('/api/proxy')) {
                            // 使用代理
                            context.url = `/api/proxy?url=${encodeURIComponent(url)}`
                            console.log('HLS Loader using proxy for:', url, '→', context.url)
                          }
                        } catch (error) {
                          console.warn('HLS Loader URL parsing error:', error)
                        }
                      }

                      // 调用原始加载器
                      super.load(context, config, callbacks)
                    }
                  },
                  drmSystems: {},
                  requestMediaKeySystemAccessFunc: undefined,
                })
                
                hlsRef.current = hls
                hls.loadSource(url)
                hls.attachMedia(video)
                
                // HLS事件处理
                hls.on(Hls.Events.MANIFEST_PARSED, () => {
                  console.log('HLS manifest parsed')
                })
                
                hls.on(Hls.Events.ERROR, (event, data) => {
                  console.error('HLS error:', data)

                  // 处理非致命错误
                  if (!data.fatal) {
                    switch (data.details) {
                      case Hls.ErrorDetails.FRAG_PARSING_ERROR:
                        console.warn('Fragment parsing error (non-fatal):', data.reason)
                        // 对于解析错误，通常HLS.js会自动跳过有问题的片段
                        // 我们只需要记录错误，不需要特殊处理
                        break
                      case Hls.ErrorDetails.FRAG_LOAD_ERROR:
                        console.warn('Fragment load error (non-fatal):', data)
                        break
                      case Hls.ErrorDetails.BUFFER_APPEND_ERROR:
                        console.warn('Buffer append error (non-fatal):', data)
                        break
                      default:
                        console.warn('Non-fatal HLS error:', data.details, data)
                        break
                    }
                    return
                  }

                  // 处理致命错误
                  switch (data.type) {
                    case Hls.ErrorTypes.NETWORK_ERROR:
                      console.log('Fatal network error, trying to recover...')
                      try {
                        hls.startLoad()
                      } catch (err) {
                        console.error('Failed to recover from network error:', err)
                        setError('网络连接失败，请检查网络或稍后重试')
                      }
                      break
                    case Hls.ErrorTypes.MEDIA_ERROR:
                      console.log('Fatal media error, trying to recover...')
                      try {
                        hls.recoverMediaError()
                      } catch (err) {
                        console.error('Failed to recover from media error:', err)
                        setError('媒体解码失败，该频道可能不兼容')
                      }
                      break
                    default:
                      console.log('Unrecoverable fatal error, destroying HLS...')
                      setError('播放失败，该频道可能存在问题')
                      try {
                        hls.destroy()
                      } catch (err) {
                        console.error('Error destroying HLS:', err)
                      }
                      break
                  }
                })
              } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                // 原生HLS支持
                video.src = url
              } else {
                onError?.({
                  code: 'HLS_NOT_SUPPORTED',
                  message: '当前浏览器不支持HLS播放'
                })
              }
            }
          },
          // 控制栏配置
          controls: [
            {
              position: 'right',
              html: '画质',
              tooltip: '画质设置',
              click: function () {
                console.log('Quality settings clicked')
              },
            },
          ],
          // 右键菜单
          contextmenu: [
            {
              html: '复制视频地址',
              click: function () {
                if (channel?.url) {
                  navigator.clipboard.writeText(channel.url)
                }
              },
            },
            {
              html: '视频统计信息',
              click: function () {
                console.log('Video stats:', player.video)
              },
            },
          ],
          // 图标配置 - 优化loading图标，减少视觉干扰
          icons: {
            loading: '<div class="loading-spinner-optimized"></div>',
            state: '<div class="play-state-optimized"></div>',
          },
        })

        playerRef.current = player

        // 注册实例到跟踪器
        if (containerRef.current) {
          playerInstances.set(containerRef.current, player)
        }

        console.log('ArtPlayer created successfully:', {
          player: !!player,
          template: !!player.template,
          video: !!player.video,
          container: !!player.template?.container,
          containerElement: !!containerRef.current
        })

        setIsPlayerReady(true)
        initializingRef.current = false
        onReady?.()

        // 播放器事件监听
        player.on('ready', () => {
          console.log('Player ready')
        })

        player.on('video:loadstart', () => {
          setIsLoading(true)
          setError(null)
        })

        player.on('video:canplay', () => {
          setIsLoading(false)
          // 只有在设置启用自动播放时才自动播放
          if (defaultConfig.autoplay) {
            setTimeout(() => {
              if (player && !player.playing) {
                console.log('Attempting to auto-play video (enabled in settings)')
                player.play().catch((error: any) => {
                  console.warn('Auto-play failed:', error)
                })
              }
            }, 500)
          }
        })

        player.on('video:error', (err: any) => {
          setIsLoading(false)
          const errorMessage = '视频加载失败'
          setError(errorMessage)
          onError?.({
            code: 'VIDEO_ERROR',
            message: errorMessage,
            details: err
          })
        })

        player.on('video:ended', () => {
          console.log('Video ended')
        })

        // 添加更多诊断事件
        player.on('video:loadedmetadata', () => {
          console.log('Video metadata loaded')
        })

        player.on('video:loadeddata', () => {
          console.log('Video data loaded')
        })

        player.on('video:play', () => {
          console.log('Video playing')
        })

        player.on('video:pause', () => {
          console.log('Video paused')
        })

        player.on('video:waiting', () => {
          console.log('Video waiting for data')
        })

        player.on('video:seeking', () => {
          console.log('Video seeking')
        })

        player.on('video:seeked', () => {
          console.log('Video seeked')
        })

      } catch (error) {
        console.error('Failed to initialize player:', error)
        const errorMessage = '播放器初始化失败'
        setError(errorMessage)
        initializingRef.current = false
        onError?.({
          code: 'PLAYER_INIT_ERROR',
          message: errorMessage,
          details: error
        })
      }
    }

    initPlayer()

    // 清理函数
    return () => {
      isMounted = false
      initializingRef.current = false

      if (hlsRef.current) {
        try {
          hlsRef.current.destroy()
        } catch (error) {
          console.warn('Error destroying HLS instance:', error)
        }
        hlsRef.current = null
      }
      if (playerRef.current) {
        try {
          playerRef.current.destroy()
        } catch (error) {
          console.warn('Error destroying player instance:', error)
        }
        playerRef.current = null
      }

      // 清理容器和跟踪器
      if (containerRef.current) {
        try {
          playerInstances.delete(containerRef.current)
          containerRef.current.innerHTML = ''
        } catch (error) {
          console.warn('Error clearing container:', error)
        }
      }

      setIsPlayerReady(false)
      setIsLoading(false)
      setError(null)
    }
  }, [channel])

  // 切换频道
  useEffect(() => {
    if (!playerRef.current || !channel || !isPlayerReady) return

    const switchChannel = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // 更新播放器标题
        playerRef.current.title = channel.name
        // 不设置poster，避免显示大的频道图标

        // 处理 URL，对所有外部 HTTP/HTTPS URL 使用代理
        let videoUrl = channel.url

        // 检查是否需要使用代理
        if (videoUrl.startsWith('http://') || videoUrl.startsWith('https://')) {
          try {
            const url = new URL(videoUrl)
            // 只有真正的本地地址才不使用代理
            const isLocalhost = url.hostname === 'localhost' ||
                               url.hostname === '127.0.0.1' ||
                               url.hostname === '0.0.0.0'

            if (!isLocalhost) {
              // 对所有非 localhost 的地址使用代理（包括局域网地址）
              videoUrl = `/api/proxy?url=${encodeURIComponent(videoUrl)}`
              console.log('Using proxy for URL:', channel.url, '→', videoUrl)
            }
          } catch (error) {
            console.warn('URL parsing error:', error)
          }
        }

        // 切换视频源
        if (videoUrl.includes('.m3u8')) {
          playerRef.current.type = 'm3u8'
        }

        playerRef.current.switchUrl(videoUrl)
        
      } catch (error) {
        console.error('Failed to switch channel:', error)
        const errorMessage = '频道切换失败'
        setError(errorMessage)
        onError?.({
          code: 'CHANNEL_SWITCH_ERROR',
          message: errorMessage,
          details: error
        })
      }
    }

    switchChannel()
  }, [channel, isPlayerReady])

  // 调试信息（仅在开发环境中显示）
  if (process.env.NODE_ENV === 'development') {
    console.log('Player render state:', {
      channel: channel?.name,
      isLoading,
      error,
      isPlayerReady,
      containerRef: !!containerRef.current
    })
  }

  return (
    <div className={cn('relative w-full h-full bg-black rounded-lg overflow-hidden', className)}>
      {/* 播放器容器 */}
      <div
        ref={containerRef}
        className="w-full h-full"
        style={{
          minHeight: '400px',
          height: '100%',
          width: '100%',
          position: 'relative'
        }}
      />

      {/* 简化的状态显示 */}
      {!channel && (
        <div className="absolute top-4 left-4 text-white text-sm bg-black bg-opacity-75 p-3 rounded">
          请选择一个频道开始播放
        </div>
      )}

      {channel && !isPlayerReady && !isLoading && !error && (
        <div className="absolute top-4 left-4 text-white text-sm bg-black bg-opacity-75 p-3 rounded">
          正在初始化播放器...
        </div>
      )}

      {/* 加载状态 - 简化显示，避免与主页面加载状态冲突 */}
      {isLoading && (
        <div className="absolute top-4 right-4 text-white text-sm bg-black bg-opacity-75 p-3 rounded z-10">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            <span>连接中...</span>
          </div>
        </div>
      )}

      {/* 错误状态 */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75 z-10">
          <div className="text-center text-white p-4">
            <div className="text-red-400 text-lg mb-2">播放错误</div>
            <div className="text-sm opacity-75">{error}</div>
            <button
              onClick={() => {
                setError(null)
                if (channel) {
                  // 重试播放
                  playerRef.current?.switchUrl(channel.url)
                }
              }}
              className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-sm transition-colors"
            >
              重试
            </button>
          </div>
        </div>
      )}

      {/* 无频道状态 */}
      {!channel && !isLoading && !error && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 z-10">
          <div className="text-center text-gray-400">
            <div className="text-6xl mb-4">📺</div>
            <div className="text-lg">请选择要播放的 IPTV 频道</div>
          </div>
        </div>
      )}

      {/* 播放器就绪但无控件的情况 */}
      {channel && isPlayerReady && !isLoading && !error && (
        <div className="absolute bottom-4 left-4 text-white text-sm bg-black bg-opacity-50 p-2 rounded">
          播放器已就绪 - 如果看不到控件，请检查ArtPlayer初始化
        </div>
      )}
    </div>
  )
}

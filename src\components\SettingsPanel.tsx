'use client'

import React, { useState, useEffect } from 'react'
import { X, Volume2, Monitor, Globe, Trash2, Info, RefreshCw, Clock, VolumeX } from 'lucide-react'
import { UserSettings } from '@/types'
import { cn, storage } from '@/lib/utils'
import ThemeToggle from './ThemeToggle'

interface SettingsPanelProps {
  isOpen: boolean
  onClose: () => void
  className?: string
  autoRefreshEnabled: boolean
  refreshInterval: number
  onAutoRefreshChange: (enabled: boolean) => void
  onRefreshIntervalChange: (interval: number) => void
}

const defaultSettings: UserSettings = {
  theme: 'dark',
  language: 'zh-CN',
  autoplay: false,
  volume: 0.7,
  quality: 'auto',
  subtitles: false,
  fullscreenOnPlay: false,
  rememberPosition: true,
  startMuted: true
}

export default function SettingsPanel({
  isOpen,
  onClose,
  className,
  autoRefreshEnabled,
  refreshInterval,
  onAutoRefreshChange,
  onRefreshIntervalChange
}: SettingsPanelProps) {
  const [settings, setSettings] = useState<UserSettings>(defaultSettings)
  const [activeTab, setActiveTab] = useState<'general' | 'player' | 'about'>('general')

  // 加载设置
  useEffect(() => {
    const savedSettings = storage.get('userSettings', defaultSettings)
    setSettings(savedSettings)
  }, [])

  // 保存设置
  const updateSetting = <K extends keyof UserSettings>(key: K, value: UserSettings[K]) => {
    const newSettings = { ...settings, [key]: value }
    setSettings(newSettings)
    storage.set('userSettings', newSettings)
  }

  const clearData = () => {
    if (confirm('确定要清除所有数据吗？这将删除所有设置、播放历史和收藏。')) {
      storage.clear()
      setSettings(defaultSettings)
      window.location.reload()
    }
  }



  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className={cn(
        'bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden',
        className
      )}>
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">设置</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* 侧边栏 */}
          <div className="w-48 border-r border-gray-200 dark:border-gray-700 p-4">
            <nav className="space-y-2">
              <button
                onClick={() => setActiveTab('general')}
                className={cn(
                  'w-full text-left px-3 py-2 rounded-lg transition-colors',
                  activeTab === 'general'
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                )}
              >
                常规设置
              </button>
              <button
                onClick={() => setActiveTab('player')}
                className={cn(
                  'w-full text-left px-3 py-2 rounded-lg transition-colors',
                  activeTab === 'player'
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                )}
              >
                播放器设置
              </button>
              <button
                onClick={() => setActiveTab('about')}
                className={cn(
                  'w-full text-left px-3 py-2 rounded-lg transition-colors',
                  activeTab === 'about'
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                )}
              >
                关于
              </button>
            </nav>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 p-6 overflow-y-auto">
            {activeTab === 'general' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    外观设置
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          主题模式
                        </label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          选择应用的外观主题
                        </p>
                      </div>
                      <ThemeToggle />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          语言
                        </label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          选择界面语言
                        </p>
                      </div>
                      <select
                        value={settings.language}
                        onChange={(e) => updateSetting('language', e.target.value)}
                        className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      >
                        <option value="zh-CN">简体中文</option>
                        <option value="zh-TW">繁體中文</option>
                        <option value="en-US">English</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    自动刷新设置
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          启用定时刷新
                        </label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          自动定时刷新所有订阅源并更新频道列表
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={autoRefreshEnabled}
                          onChange={(e) => onAutoRefreshChange(e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          刷新间隔
                        </label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          设置自动刷新所有订阅源的时间间隔
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4 text-gray-400" />
                        <select
                          value={refreshInterval}
                          onChange={(e) => onRefreshIntervalChange(parseInt(e.target.value))}
                          disabled={!autoRefreshEnabled}
                          className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:opacity-50"
                        >
                          <option value={1}>1分钟</option>
                          <option value={5}>5分钟</option>
                          <option value={10}>10分钟</option>
                          <option value={15}>15分钟</option>
                          <option value={30}>30分钟</option>
                          <option value={60}>1小时</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    数据管理
                  </h3>
                  <div className="space-y-4">


                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          清除数据
                        </label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          清除所有本地数据
                        </p>
                      </div>
                      <button
                        onClick={clearData}
                        className="flex items-center space-x-2 px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                        <span>清除</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'player' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    播放设置
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          自动播放
                        </label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          选择频道后自动开始播放
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.autoplay}
                          onChange={(e) => updateSetting('autoplay', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          默认音量
                        </label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          播放器的默认音量大小
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Volume2 className="w-4 h-4 text-gray-400" />
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.1"
                          value={settings.volume}
                          onChange={(e) => updateSetting('volume', parseFloat(e.target.value))}
                          className="w-20"
                        />
                        <span className="text-sm text-gray-500 w-8">
                          {Math.round(settings.volume * 100)}%
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          默认画质
                        </label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          播放器的默认画质设置
                        </p>
                      </div>
                      <select
                        value={settings.quality}
                        onChange={(e) => updateSetting('quality', e.target.value as any)}
                        className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      >
                        <option value="auto">自动</option>
                        <option value="high">高清</option>
                        <option value="medium">标清</option>
                        <option value="low">流畅</option>
                      </select>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          播放时全屏
                        </label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          开始播放时自动进入全屏模式
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.fullscreenOnPlay}
                          onChange={(e) => updateSetting('fullscreenOnPlay', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          开始播放时静音
                        </label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          播放开始时自动静音
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.startMuted}
                          onChange={(e) => updateSetting('startMuted', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          记住播放位置
                        </label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          记住上次播放的位置
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.rememberPosition}
                          onChange={(e) => updateSetting('rememberPosition', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'about' && (
              <div className="space-y-6">
                <div className="text-center">
                  <div className="text-6xl mb-4">📺</div>
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                    IPTV Player
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">
                    现代化的IPTV网页播放器
                  </p>
                  <div className="inline-flex items-center px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-full text-sm">
                    版本 1.0.0
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                      功能特性
                    </h4>
                    <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                      <li>• 支持HLS (M3U8) 直播流播放</li>
                      <li>• M3U/M3U8订阅源管理</li>
                      <li>• 响应式设计，支持移动端</li>
                      <li>• 深色/浅色主题切换</li>
                      <li>• 频道搜索和分类</li>
                      <li>• 播放历史记录</li>
                    </ul>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                      技术栈
                    </h4>
                    <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                      <li>• Next.js 15 + React 18</li>
                      <li>• TypeScript + Tailwind CSS</li>
                      <li>• ArtPlayer + HLS.js</li>
                      <li>• SQLite 数据库</li>
                      <li>• Docker 容器化部署</li>
                    </ul>
                  </div>

                  <div className="text-center text-sm text-gray-500 dark:text-gray-400">
                    <p>© 2024 IPTV Player. All rights reserved.</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

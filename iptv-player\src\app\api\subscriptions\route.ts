import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { fetchM3U, parseM3U } from '@/lib/m3u-parser'

export async function GET() {
  try {
    const subscriptions = await prisma.subscription.findMany({
      include: {
        _count: {
          select: { channels: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    const subscriptionsWithCount = subscriptions.map(sub => ({
      ...sub,
      channelCount: sub._count.channels
    }))

    return NextResponse.json(subscriptionsWithCount)
  } catch (error) {
    console.error('Error fetching subscriptions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch subscriptions' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, url, type = 'M3U' } = body

    if (!name || !url) {
      return NextResponse.json(
        { error: 'Name and URL are required' },
        { status: 400 }
      )
    }

    // 检查URL是否已存在
    const existingSubscription = await prisma.subscription.findUnique({
      where: { url }
    })

    if (existingSubscription) {
      return NextResponse.json(
        { error: 'Subscription with this URL already exists' },
        { status: 409 }
      )
    }

    // 使用事务处理订阅源创建和频道导入
    const result = await prisma.$transaction(async (tx) => {
      // 创建订阅源
      const subscription = await tx.subscription.create({
        data: {
          name,
          url,
          type,
          isActive: true
        }
      })

      // 尝试获取并解析M3U内容
      try {
        const m3uContent = await fetchM3U(url)
        const parsed = await parseM3U(m3uContent)

        // 批量创建频道
        if (parsed.channels.length > 0) {
          // 由于唯一约束，使用try-catch处理重复数据
          try {
            await tx.channel.createMany({
              data: parsed.channels.map(channel => ({
                name: channel.name,
                url: channel.url,
                logo: channel.logo,
                group: channel.group,
                subscriptionId: subscription.id
              }))
            })
          } catch (error) {
            // 如果有重复数据，逐个插入并忽略错误
            for (const channel of parsed.channels) {
              try {
                await tx.channel.create({
                  data: {
                    name: channel.name,
                    url: channel.url,
                    logo: channel.logo,
                    group: channel.group,
                    subscriptionId: subscription.id
                  }
                })
              } catch {
                // 忽略重复数据错误
              }
            }
          }

          // 更新订阅源的最后更新时间
          await tx.subscription.update({
            where: { id: subscription.id },
            data: { lastUpdated: new Date() }
          })
        }
      } catch (parseError) {
        console.error('Error parsing M3U:', parseError)
        // 解析失败时抛出错误，回滚事务
        throw new Error(`Failed to parse M3U: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`)
      }

      return subscription
    })

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error('Error creating subscription:', error)

    if (error instanceof Error && error.message.includes('Failed to parse M3U')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create subscription' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, isActive } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Subscription ID is required' },
        { status: 400 }
      )
    }

    const subscription = await prisma.subscription.update({
      where: { id },
      data: { isActive }
    })

    return NextResponse.json(subscription)
  } catch (error) {
    console.error('Error updating subscription:', error)
    return NextResponse.json(
      { error: 'Failed to update subscription' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Subscription ID is required' },
        { status: 400 }
      )
    }

    // 删除订阅源（会级联删除相关频道）
    await prisma.subscription.delete({
      where: { id }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting subscription:', error)
    return NextResponse.json(
      { error: 'Failed to delete subscription' },
      { status: 500 }
    )
  }
}

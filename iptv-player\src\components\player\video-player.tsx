"use client"

import { useRef, useState } from 'react';
import HlsPlayer, { HlsPlayerRef } from './hls-player';

interface Channel {
  id: string;
  name: string;
  url: string;
  group?: string;
  logo?: string;
}

interface VideoPlayerProps {
  channel: Channel | null;
  onError?: (error: string) => void;
}

export default function VideoPlayer({ channel, onError }: VideoPlayerProps) {
  const playerRef = useRef<HlsPlayerRef>(null);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(0.7);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [qualityLevels, setQualityLevels] = useState<any[]>([]);
  const [currentQuality, setCurrentQuality] = useState(-1);

  const addDebugInfo = (info: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugInfo(prev => [...prev.slice(-9), `[${timestamp}] ${info}`]);
    console.log(info);
  };

  const handleError = (error: any) => {
    const errorMsg = `播放错误: ${error.message || error.details || '未知错误'}`;
    addDebugInfo(errorMsg);
    onError?.(errorMsg);
  };

  const handleReady = () => {
    addDebugInfo(`✅ 播放器就绪: ${channel?.name}`);
  };

  const handlePlay = () => {
    setIsPlaying(true);
    addDebugInfo('▶️ 开始播放');
  };

  const handlePause = () => {
    setIsPlaying(false);
    addDebugInfo('⏸️ 播放暂停');
  };

  const handleTimeUpdate = (time: number) => {
    setCurrentTime(time);
  };

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume);
    playerRef.current?.setVolume(newVolume);
    addDebugInfo(`🔊 音量调整: ${Math.round(newVolume * 100)}%`);
  };

  const handleToggleMute = () => {
    playerRef.current?.toggleMute();
    const muted = playerRef.current?.isMuted() || false;
    setIsMuted(muted);
    addDebugInfo(muted ? '🔇 静音开启' : '🔊 静音关闭');
  };

  const handleToggleFullscreen = async () => {
    try {
      if (isFullscreen) {
        await playerRef.current?.exitFullscreen();
        setIsFullscreen(false);
        addDebugInfo('🔳 退出全屏');
      } else {
        await playerRef.current?.requestFullscreen();
        setIsFullscreen(true);
        addDebugInfo('🔲 进入全屏');
      }
    } catch (error) {
      addDebugInfo(`❌ 全屏切换失败: ${error}`);
    }
  };

  const handleQualityChange = (levels: any[], currentLevel: number) => {
    setQualityLevels(levels);
    setCurrentQuality(currentLevel);
    const qualityInfo = currentLevel === -1 ? '自动' : `${levels[currentLevel]?.height}p`;
    addDebugInfo(`🎯 画质信息更新: ${qualityInfo} (共${levels.length}个画质)`);
  };

  const handleQualitySelect = (level: number) => {
    playerRef.current?.setQualityLevel(level);
    const qualityInfo = level === -1 ? '自动' : `${qualityLevels[level]?.height}p`;
    addDebugInfo(`🎯 手动切换画质: ${qualityInfo}`);
  };

  if (!channel) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-900 text-white">
        <div className="text-center">
          <div className="text-6xl mb-4">📺</div>
          <div className="text-xl">请选择一个频道开始观看</div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 relative">
        <HlsPlayer
          ref={playerRef}
          src={channel.url}
          onError={handleError}
          onReady={handleReady}
          onPlay={handlePlay}
          onPause={handlePause}
          onTimeUpdate={handleTimeUpdate}
          onQualityChange={handleQualityChange}
          className="w-full h-full"
          autoPlay={false}
          muted={false}
        />
        
        {/* 播放器状态指示器 */}
        <div className="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded text-sm">
          正在播放: {channel.name}
          {isPlaying && (
            <span className="ml-2 text-green-400">
              ● 直播中
            </span>
          )}
        </div>

        {/* 播放控制面板 */}
        <div className="absolute bottom-4 right-4 bg-black/70 rounded-lg p-3 flex flex-col gap-2">
          {/* 播放控制按钮 */}
          <div className="flex gap-2">
            <button
              onClick={() => playerRef.current?.play()}
              className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors"
              disabled={isPlaying}
            >
              ▶️ 播放
            </button>
            <button
              onClick={() => playerRef.current?.pause()}
              className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors"
              disabled={!isPlaying}
            >
              ⏸️ 暂停
            </button>
            <button
              onClick={handleToggleMute}
              className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
            >
              {isMuted ? '🔇' : '🔊'}
            </button>
            <button
              onClick={handleToggleFullscreen}
              className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm transition-colors"
            >
              {isFullscreen ? '🔳' : '🔲'}
            </button>
          </div>

          {/* 音量控制 */}
          <div className="flex items-center gap-2 text-white text-sm">
            <span className="text-xs">音量:</span>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={volume}
              onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
              className="w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
            />
            <span className="text-xs w-8">{Math.round(volume * 100)}%</span>
          </div>

          {/* 画质选择 */}
          {qualityLevels.length > 1 && (
            <div className="flex items-center gap-2 text-white text-sm">
              <span className="text-xs">画质:</span>
              <select
                value={currentQuality}
                onChange={(e) => handleQualitySelect(parseInt(e.target.value))}
                className="bg-gray-700 text-white text-xs rounded px-2 py-1 border-none outline-none"
              >
                <option value={-1}>自动</option>
                {qualityLevels.map((level, index) => (
                  <option key={index} value={index}>
                    {level.height}p ({Math.round(level.bitrate / 1000)}k)
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>
      
      {/* 调试信息面板 */}
      <div className="bg-gray-900 text-green-400 p-4 max-h-48 overflow-y-auto">
        <div className="text-sm font-mono">
          <div className="text-white mb-2 flex items-center justify-between">
            <span>🔧 调试信息 (基于HLS.js):</span>
            <span className="text-xs text-gray-400">
              频道: {channel.name} | URL: {channel.url}
            </span>
          </div>
          {debugInfo.map((info, index) => (
            <div key={index} className="mb-1">
              {info}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

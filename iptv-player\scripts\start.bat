@echo off
chcp 65001 >nul
echo 🚀 启动 IPTV Player...

REM 检查 Docker 是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

REM 检查 Docker Compose 是否安装
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose 未安装，请先安装 Docker Compose
    pause
    exit /b 1
)

REM 创建必要的目录
echo 📁 创建数据目录...
if not exist "data" mkdir data
if not exist "config" mkdir config

REM 检查环境变量文件
if not exist ".env" (
    echo 📝 创建环境变量文件...
    copy ".env.example" ".env" >nul
    echo ⚠️  请编辑 .env 文件配置您的环境变量
)

REM 构建并启动服务
echo 🔨 构建并启动服务...
docker-compose up -d --build

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
docker-compose ps | findstr "Up" >nul
if %errorlevel% equ 0 (
    echo ✅ IPTV Player 启动成功！
    echo 🌐 访问地址: http://localhost:9004
    echo 📊 查看日志: docker-compose logs -f
    echo 🛑 停止服务: docker-compose down
) else (
    echo ❌ 服务启动失败，请查看日志：
    docker-compose logs
)

pause
